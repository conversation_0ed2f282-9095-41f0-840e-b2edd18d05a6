/**
 * Diagnostic Loading Test - Verify loading screen removal
 * 
 * This script helps verify that the diagnostic module loading screen
 * has been properly removed and the universal loading system is working.
 */

(function($) {
    'use strict';

    const DiagnosticLoadingTest = {
        
        /**
         * Test if diagnostic loading screen is removed
         */
        testLoadingScreenRemoval: function() {
            console.log('🔍 Testing Diagnostic Loading Screen Removal...');
            
            const results = {
                moduleSpecificOverlay: false,
                moduleSpecificCSS: false,
                moduleSpecificJS: false,
                universalSystemWorking: false
            };
            
            // Check for module-specific overlay in DOM
            const diagnosticOverlay = $('#redco-diagnostic-loading-overlay');
            results.moduleSpecificOverlay = diagnosticOverlay.length > 0;
            
            // Check for module-specific CSS classes
            const diagnosticLoadingCSS = $('.redco-diagnostic-loading-overlay, .redco-loading-content, .redco-loading-spinner');
            results.moduleSpecificCSS = diagnosticLoadingCSS.length > 0;
            
            // Check if universal loading system exists
            const universalOverlay = $('#redco-universal-loading-overlay');
            results.universalSystemWorking = universalOverlay.length === 0; // Should not exist when not loading
            
            // Check if DiagnosticAutoFix object has loading methods
            if (window.DiagnosticAutoFix) {
                results.moduleSpecificJS = typeof window.DiagnosticAutoFix.showInitialLoadingScreen === 'function';
            }
            
            console.log('📊 Test Results:', results);
            
            // Determine overall status
            const isFixed = !results.moduleSpecificOverlay && 
                           !results.moduleSpecificCSS && 
                           !results.moduleSpecificJS;
            
            if (isFixed) {
                console.log('✅ SUCCESS: Diagnostic loading screen has been properly removed!');
                console.log('✅ Module now uses universal loading system');
            } else {
                console.log('❌ ISSUES DETECTED:');
                if (results.moduleSpecificOverlay) {
                    console.log('  - Module-specific overlay still exists in DOM');
                }
                if (results.moduleSpecificCSS) {
                    console.log('  - Module-specific CSS classes still present');
                }
                if (results.moduleSpecificJS) {
                    console.log('  - Module-specific JS loading methods still exist');
                }
            }
            
            return results;
        },

        /**
         * Test tab navigation to diagnostic module
         */
        testTabNavigation: function() {
            console.log('🔍 Testing Tab Navigation to Diagnostic Module...');
            
            const currentUrl = window.location.href;
            const diagnosticUrl = currentUrl.split('?')[0] + '?page=redco-optimizer&tab=diagnostic-autofix';
            
            console.log('🚀 Navigating to diagnostic tab...');
            console.log('📍 URL:', diagnosticUrl);
            
            // Navigate to diagnostic tab
            window.location.href = diagnosticUrl;
        },

        /**
         * Monitor for stuck loading states
         */
        monitorLoadingStates: function() {
            console.log('👁️ Monitoring for stuck loading states...');
            
            let checkCount = 0;
            const maxChecks = 30; // 30 seconds
            
            const monitor = setInterval(() => {
                checkCount++;
                
                const diagnosticOverlay = $('#redco-diagnostic-loading-overlay');
                const universalOverlay = $('#redco-universal-loading-overlay');
                const stuckMessage = $('*:contains("Stay tuned, we are working on loading your performance diagnostics")');
                
                const status = {
                    checkNumber: checkCount,
                    diagnosticOverlayVisible: diagnosticOverlay.is(':visible'),
                    universalOverlayVisible: universalOverlay.is(':visible'),
                    stuckMessageFound: stuckMessage.length > 0,
                    timestamp: new Date().toLocaleTimeString()
                };
                
                console.log('📊 Loading State Check:', status);
                
                // Check for stuck states
                if (status.diagnosticOverlayVisible) {
                    console.warn('⚠️ STUCK: Diagnostic overlay is still visible!');
                    clearInterval(monitor);
                    return;
                }
                
                if (status.stuckMessageFound) {
                    console.warn('⚠️ STUCK: "Stay tuned" message found in DOM!');
                    clearInterval(monitor);
                    return;
                }
                
                if (checkCount >= maxChecks) {
                    console.log('✅ Monitoring complete - no stuck states detected');
                    clearInterval(monitor);
                    return;
                }
                
                // If universal overlay disappears, content should be loaded
                if (!status.universalOverlayVisible && checkCount > 3) {
                    console.log('✅ Universal loading completed successfully');
                    clearInterval(monitor);
                    return;
                }
                
            }, 1000); // Check every second
        },

        /**
         * Force clear any stuck loading screens
         */
        forceClearLoadingScreens: function() {
            console.log('🧹 Force clearing any stuck loading screens...');
            
            // Remove diagnostic-specific overlays
            $('#redco-diagnostic-loading-overlay').remove();
            $('.redco-diagnostic-loading-overlay').remove();
            
            // Remove universal overlay if stuck
            $('#redco-universal-loading-overlay').removeClass('show');
            setTimeout(() => {
                $('#redco-universal-loading-overlay').remove();
            }, 500);
            
            // Remove any elements containing the stuck message
            $('*:contains("Stay tuned, we are working on loading your performance diagnostics")').remove();
            
            console.log('✅ Loading screens cleared');
        },

        /**
         * Run complete diagnostic test
         */
        runCompleteTest: function() {
            console.log('🚀 Running Complete Diagnostic Loading Test...');
            console.log('================================================');
            
            // Test 1: Check if loading screen is removed
            this.testLoadingScreenRemoval();
            
            // Test 2: Monitor for stuck states
            this.monitorLoadingStates();
            
            // Test 3: Check current page state
            setTimeout(() => {
                const currentTab = new URLSearchParams(window.location.search).get('tab');
                if (currentTab === 'diagnostic-autofix') {
                    console.log('✅ Currently on diagnostic tab - checking content...');
                    
                    const hasContent = $('.diagnostic-layout, .redco-module-content').length > 0;
                    const hasCards = $('.redco-card:visible').length > 0;
                    
                    console.log('📊 Content Check:', {
                        hasContent: hasContent,
                        hasCards: hasCards,
                        status: hasContent && hasCards ? '✅ Content loaded' : '❌ Content missing'
                    });
                } else {
                    console.log('ℹ️ Not on diagnostic tab - use testTabNavigation() to navigate');
                }
            }, 2000);
            
            console.log('================================================');
        }
    };

    // Make globally accessible
    window.DiagnosticLoadingTest = DiagnosticLoadingTest;

    // Auto-run test when on diagnostic tab
    $(document).ready(function() {
        if (window.location.href.includes('page=redco-optimizer')) {
            console.log('🔧 Diagnostic Loading Test utilities loaded');
            console.log('📝 Available commands:');
            console.log('  DiagnosticLoadingTest.testLoadingScreenRemoval() - Check if loading screen is removed');
            console.log('  DiagnosticLoadingTest.testTabNavigation() - Navigate to diagnostic tab');
            console.log('  DiagnosticLoadingTest.monitorLoadingStates() - Monitor for stuck states');
            console.log('  DiagnosticLoadingTest.forceClearLoadingScreens() - Force clear stuck screens');
            console.log('  DiagnosticLoadingTest.runCompleteTest() - Run all tests');
            
            // Auto-run if on diagnostic tab
            const currentTab = new URLSearchParams(window.location.search).get('tab');
            if (currentTab === 'diagnostic-autofix') {
                setTimeout(() => {
                    DiagnosticLoadingTest.runCompleteTest();
                }, 1000);
            }
        }
    });

})(jQuery);

<?php
/**
 * Smart WebP Conversion Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if module is enabled
$is_enabled = redco_is_module_enabled('smart-webp-conversion');

// ARCHITECTURAL CLEANUP: Use only Enhanced WebP class
$webp_module = null;
if (class_exists('Redco_Smart_WebP_Conversion_Enhanced')) {
    $webp_module = new Redco_Smart_WebP_Conversion_Enhanced();
} elseif (class_exists('Redco_Smart_WebP_Conversion')) {
    // Fallback for backward compatibility
    $webp_module = new Redco_Smart_WebP_Conversion();
}

// Get current settings using standardized helper functions with safe defaults
$auto_convert_uploads = redco_get_module_option('smart-webp-conversion', 'auto_convert_uploads', true) ?? true;
$replace_in_content = redco_get_module_option('smart-webp-conversion', 'replace_in_content', true) ?? true;
$quality = redco_get_module_option('smart-webp-conversion', 'quality', 85) ?? 85;
$lossless = redco_get_module_option('smart-webp-conversion', 'lossless', false) ?? false;
$backup_originals = redco_get_module_option('smart-webp-conversion', 'backup_originals', true) ?? true;
$batch_size = redco_get_module_option('smart-webp-conversion', 'batch_size', 10) ?? 10;



// Get conversion statistics - check server support regardless of module state
$server_support = function_exists('imagewebp') && (imagetypes() & IMG_WEBP);

$webp_stats = array(
    'total_images' => 0,
    'converted_images' => 0,
    'unconverted_images' => 0,
    'conversion_percentage' => 0,
    'total_savings' => 0,
    'savings_percentage' => 0,
    'server_support' => $server_support,
    'browser_support' => isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false
);
$recent_conversions = array();
$optimization_tips = array();

// Get actual stats from module instance
if ($webp_module) {
    $webp_stats = $webp_module->get_stats();
    $recent_conversions = array_slice($webp_stats['recent_conversions'], 0, 5);
    $optimization_tips = $webp_module->get_optimization_tips();


} else {
    // Fallback to server support check only
    $webp_stats['server_support'] = $server_support;


}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<!-- Enqueue WebP module specific assets -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>modules/smart-webp-conversion/assets/css/toast-notifications.css">
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>modules/smart-webp-conversion/assets/css/enhanced-features.css">
<script src="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>modules/smart-webp-conversion/assets/js/toast-notifications.js"></script>

<div class="redco-module-tab" data-module="smart-webp-conversion">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Smart WebP Conversion', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-format-image"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Smart WebP Conversion', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Automatically convert images to WebP format for better performance while maintaining compatibility with all browsers', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($webp_stats['server_support']): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Server Compatible', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($webp_stats['converted_images'] > 0): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-format-image"></span>
                                    <?php _e('Images Converted', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="test-webp-support" data-redco-action="test_webp_support">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Test Support', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="header-bulk-convert-images" <?php echo !$webp_stats['server_support'] ? 'disabled' : ''; ?>>
                                <span class="dashicons dashicons-update"></span>
                                <?php _e('Convert All', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($webp_stats['converted_images']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Converted', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $webp_stats['conversion_percentage']; ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Rate', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo size_format($webp_stats['total_savings']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Saved', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">

                <!-- Server Compatibility Check -->
                <?php if (!$webp_stats['server_support']): ?>
                <div class="redco-card compatibility-warning">
                    <div class="card-header">
                        <h3>
                            <span class="dashicons dashicons-warning"></span>
                            <?php _e('Server Compatibility Issue', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="warning-message">
                            <p><?php _e('Your server does not support WebP image conversion. Please contact your hosting provider to enable GD library with WebP support.', 'redco-optimizer'); ?></p>
                            <div class="warning-actions">
                                <button type="button" class="button button-secondary" id="test-webp-support">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Test Server Support', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <form class="redco-module-form" data-module="smart-webp-conversion">
                    <!-- Conversion Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('WebP Conversion Configuration', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="reset-webp-settings">
                                    <?php _e('Reset to Defaults', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Configure how images are converted to WebP format. WebP provides superior compression compared to JPEG and PNG while maintaining quality.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Current Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="webp-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-format-image"></span>
                                    <?php _e('Conversion Behavior Settings', 'redco-optimizer'); ?>
                                </h4>

                                <div class="webp-options">
                                    <div class="webp-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[auto_convert_uploads]" id="auto_convert_uploads" <?php checked($auto_convert_uploads); ?> class="webp-checkbox">
                                            <span class="option-icon">📤</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Auto-Convert New Uploads', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Automatically convert new image uploads to WebP format in real-time. This ensures all new images benefit from WebP compression immediately.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('High', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Processing:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Real-time', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="webp-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[replace_in_content]" id="replace_in_content" <?php checked($replace_in_content); ?> class="webp-checkbox">
                                            <span class="option-icon">🔄</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Replace Images in Content', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Automatically serve WebP versions to compatible browsers while maintaining fallback support for older browsers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Browser Support:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('95%+', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Automatic', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="webp-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[backup_originals]" id="backup_originals" <?php checked($backup_originals); ?> class="webp-checkbox">
                                            <span class="option-icon">💾</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Backup Original Images', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Keep original images as backup for rollback purposes. Recommended for safety and recovery options.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Safety:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('High', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Recovery:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Enabled', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="webp-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="estimated-savings"><?php _e('Estimated savings: 0%', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="webp-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('WebP conversion provides 25-35% smaller file sizes compared to JPEG and PNG while maintaining quality.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quality Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-image-filter"></span>
                                <?php _e('Quality & Compression Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-appearance"></span>
                                    <?php _e('Image Quality Control', 'redco-optimizer'); ?>
                                </h4>

                                <div class="setting-item enhanced">
                                    <label for="quality" class="setting-label">
                                        <strong><?php _e('WebP Quality Level', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended: 85%', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-control">
                                        <div class="quality-slider-container">
                                            <input type="range" name="settings[quality]" id="quality" min="1" max="100" value="<?php echo esc_attr($quality); ?>" class="enhanced-slider">
                                            <div class="quality-display">
                                                <span id="quality-value"><?php echo $quality; ?></span>%
                                            </div>
                                        </div>
                                        <div class="quality-presets">
                                            <button type="button" class="button button-small quality-preset" data-quality="60"><?php _e('Low (60%)', 'redco-optimizer'); ?></button>
                                            <button type="button" class="button button-small quality-preset" data-quality="85"><?php _e('Optimal (85%)', 'redco-optimizer'); ?></button>
                                            <button type="button" class="button button-small quality-preset" data-quality="95"><?php _e('High (95%)', 'redco-optimizer'); ?></button>
                                        </div>
                                        <div class="setting-help">
                                            <span class="help-icon" title="<?php _e('Higher values mean better quality but larger file sizes', 'redco-optimizer'); ?>">?</span>
                                        </div>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Set the quality level for WebP compression. Higher values mean better quality but larger file sizes. 85% provides the best balance.', 'redco-optimizer'); ?></p>
                                        <div class="setting-impact">
                                            <span class="impact-info">
                                                <span class="impact-label"><?php _e('File Size Impact:', 'redco-optimizer'); ?></span>
                                                <span class="impact-value" id="quality-impact"><?php _e('Balanced', 'redco-optimizer'); ?></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label for="lossless" class="setting-label">
                                        <strong><?php _e('Lossless Compression for PNG', 'redco-optimizer'); ?></strong>
                                    </label>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="settings[lossless]" id="lossless" <?php checked($lossless); ?>>
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <div class="setting-help">
                                            <span class="help-icon" title="<?php _e('Use lossless compression for PNG images to maintain perfect quality', 'redco-optimizer'); ?>">?</span>
                                        </div>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Use lossless compression for PNG images to maintain perfect quality. Best for images with transparency or graphics.', 'redco-optimizer'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Performance & Processing Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Batch Processing Configuration', 'redco-optimizer'); ?>
                                </h4>

                                <div class="setting-item enhanced">
                                    <label for="batch_size" class="setting-label">
                                        <strong><?php _e('Batch Processing Size', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended: 10 images', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-control">
                                        <select name="settings[batch_size]" id="batch_size" class="enhanced-select">
                                            <option value="5" <?php selected($batch_size, 5); ?> data-impact="safe"><?php _e('5 images - Safe for shared hosting', 'redco-optimizer'); ?></option>
                                            <option value="10" <?php selected($batch_size, 10); ?> data-impact="optimal"><?php _e('10 images - Optimal balance', 'redco-optimizer'); ?></option>
                                            <option value="20" <?php selected($batch_size, 20); ?> data-impact="fast"><?php _e('20 images - Fast processing', 'redco-optimizer'); ?></option>
                                            <option value="50" <?php selected($batch_size, 50); ?> data-impact="aggressive"><?php _e('50 images - High-performance servers', 'redco-optimizer'); ?></option>
                                        </select>
                                        <div class="setting-help">
                                            <span class="help-icon" title="<?php _e('Number of images to process in each batch during bulk conversion', 'redco-optimizer'); ?>">?</span>
                                        </div>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Number of images to process in each batch during bulk conversion. Lower values are safer for shared hosting.', 'redco-optimizer'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Conversion Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Conversion Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-total">
                                <span class="stat-value"><?php echo number_format($webp_stats['total_images']); ?></span>
                                <span class="stat-label"><?php _e('Total Images', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-converted">
                                <span class="stat-value"><?php echo number_format($webp_stats['converted_images']); ?></span>
                                <span class="stat-label"><?php _e('Converted', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-savings">
                                <span class="stat-value"><?php echo size_format($webp_stats['total_savings']); ?></span>
                                <span class="stat-label"><?php _e('Space Saved', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-percentage">
                                <span class="stat-value"><?php echo round($webp_stats['savings_percentage'], 1); ?>%</span>
                                <span class="stat-label"><?php _e('Avg Savings', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conversion Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Conversion Actions', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <!-- Dynamic Convert All Images Button -->
                        <div id="conversion-button-container">
                            <button type="button" id="bulk-convert-images" class="button button-primary" style="width: 100%; padding: 12px; font-size: 14px; height: auto; position: relative;" disabled>
                                <span class="dashicons dashicons-images-alt2" style="margin-right: 8px;"></span>
                                <span id="convert-button-text"><?php _e('Checking for images...', 'redco-optimizer'); ?></span>
                                <span id="convert-button-spinner" class="spinner" style="float: none; margin: 0 0 0 8px; visibility: visible;"></span>
                            </button>

                            <!-- Status Message -->
                            <div id="conversion-status-message" style="margin-top: 12px; padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #e9ecef; font-size: 13px; color: #666; display: none;">
                                <span class="dashicons dashicons-info" style="margin-right: 6px; color: #2196F3;"></span>
                                <span id="status-message-text"></span>
                            </div>
                        </div>

                        <p class="description" style="margin-top: 15px;">
                            <?php _e('Convert all existing images in your media library to WebP format for better performance.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>

                <!-- Recent Conversions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-format-image"></span>
                            <?php _e('Recent Conversions', 'redco-optimizer'); ?>
                        </h3>
                        <div class="section-actions">
                            <select id="conversions-sort" style="font-size: 12px; padding: 2px 6px;">
                                <option value="date"><?php _e('Sort by Date', 'redco-optimizer'); ?></option>
                                <option value="savings"><?php _e('Sort by Savings', 'redco-optimizer'); ?></option>
                                <option value="size"><?php _e('Sort by Size', 'redco-optimizer'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="sidebar-section-content">
                        <!-- Loading State -->
                        <div id="conversions-loading" style="text-align: center; padding: 20px; color: #666;">
                            <span class="spinner is-active" style="float: none; margin-right: 8px;"></span>
                            <?php _e('Loading conversions...', 'redco-optimizer'); ?>
                        </div>

                        <!-- Conversions List -->
                        <div id="recent-conversions-list" style="display: none;">
                            <!-- Dynamic content loaded via AJAX -->
                        </div>

                        <!-- Empty State -->
                        <div id="conversions-empty" style="display: none; text-align: center; padding: 20px; color: #666;">
                            <span class="dashicons dashicons-format-image" style="font-size: 24px; margin-bottom: 8px; display: block; opacity: 0.5;"></span>
                            <p><?php _e('No conversions yet.', 'redco-optimizer'); ?></p>
                            <p style="font-size: 12px;"><?php _e('Convert some images to see them here.', 'redco-optimizer'); ?></p>
                        </div>

                        <!-- Load More Button -->
                        <div id="conversions-load-more" style="display: none; text-align: center; margin-top: 15px;">
                            <button type="button" class="button button-small" id="load-more-conversions">
                                <span class="dashicons dashicons-arrow-down-alt2"></span>
                                <?php _e('Load More', 'redco-optimizer'); ?>
                            </button>
                        </div>

                        <!-- Pagination Info -->
                        <div id="conversions-pagination-info" style="display: none; text-align: center; margin-top: 10px; font-size: 12px; color: #666;">
                            <span id="pagination-text"></span>
                        </div>
                    </div>
                </div>

                <!-- Server Compatibility -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Server Compatibility', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="compatibility-status">
                            <div class="compatibility-item">
                                <span class="compatibility-label"><?php _e('WebP Support:', 'redco-optimizer'); ?></span>
                                <span class="compatibility-value <?php echo $webp_stats['server_support'] ? 'supported' : 'unsupported'; ?>">
                                    <?php echo $webp_stats['server_support'] ? __('Supported', 'redco-optimizer') : __('Not Supported', 'redco-optimizer'); ?>
                                </span>
                            </div>
                            <div class="compatibility-item">
                                <span class="compatibility-label"><?php _e('Browser Support:', 'redco-optimizer'); ?></span>
                                <span class="compatibility-value supported">
                                    <?php _e('95%+ browsers', 'redco-optimizer'); ?>
                                </span>
                            </div>
                        </div>
                        <?php if (!$webp_stats['server_support']): ?>
                        <button type="button" class="button button-small" id="test-webp-support" style="width: 100%; margin-top: 10px;">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Test Server Support', 'redco-optimizer'); ?>
                        </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Optimization Tips -->
                <?php if (!empty($optimization_tips)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e('Optimization Tips', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="optimization-tips-list">
                            <?php foreach ($optimization_tips as $tip): ?>
                            <div class="optimization-tip tip-<?php echo esc_attr($tip['type']); ?>">
                                <div class="tip-header">
                                    <span class="tip-icon dashicons dashicons-<?php echo $tip['type'] === 'warning' ? 'warning' : ($tip['type'] === 'success' ? 'yes-alt' : 'info'); ?>"></span>
                                    <strong><?php echo esc_html($tip['title']); ?></strong>
                                </div>
                                <div class="tip-message">
                                    <?php echo esc_html($tip['message']); ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-format-image"></span>
            <h3><?php _e('Smart WebP Conversion Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access WebP image conversion features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
/**
 * Enhanced Smart WebP Conversion Module for Redco Optimizer
 *
 * COMPLETELY REWRITTEN - Production-Ready WebP Conversion System
 *
 * Features:
 * - Bulletproof error handling and recovery
 * - Memory-efficient batch processing
 * - Real-time progress tracking
 * - Comprehensive backup system
 * - Advanced quality optimization
 * - Browser compatibility detection
 * - Detailed logging and diagnostics
 * - Performance monitoring
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Smart_WebP_Conversion_Enhanced {

    /**
     * Version for cache busting and compatibility
     */
    const VERSION = '2.0.0';

    /**
     * Supported image formats for WebP conversion
     */
    const SUPPORTED_FORMATS = array('jpg', 'jpeg', 'png', 'gif');

    /**
     * Maximum file size for conversion (in bytes) - 50MB
     */
    const MAX_FILE_SIZE = 52428800;

    /**
     * Batch processing size for bulk operations
     */
    const BATCH_SIZE = 5;

    /**
     * Memory limit threshold (80% of available memory)
     */
    const MEMORY_THRESHOLD = 0.8;

    /**
     * Default settings with enhanced options
     * CRITICAL: auto_convert_uploads defaults to FALSE for safety
     */
    private $default_settings = array(
        'auto_convert_uploads' => false, // CRITICAL: Default to disabled for user safety
        'quality' => 85,
        'lossless' => false,
        'serve_webp_to_supported_browsers' => true,
        'backup_original' => true,
        'max_width' => 2048,
        'max_height' => 2048,
        'progressive_enhancement' => true,
        'smart_quality' => true,
        'preserve_metadata' => false,
        'enable_logging' => true,
        'batch_processing' => true,
        'memory_optimization' => true
    );

    /**
     * Current settings
     */
    private $settings;

    /**
     * Conversion statistics
     */
    private $stats = array();

    /**
     * Error log
     */
    private $errors = array();

    /**
     * Processing queue
     */
    private $queue = array();

    /**
     * Initialize the module
     */
    public function __construct() {
        try {
            $this->settings = $this->get_settings();
            $this->init_hooks();
            // REMOVED: $this->init_error_handling() was causing constructor to hang
            // $this->init_error_handling();
            // REMOVED: $this->log() call was causing constructor to hang
            // $this->log('Enhanced WebP Conversion Module initialized', 'info');
        } catch (Exception $e) {
            // Silent error handling
        } catch (Error $e) {
            // Silent error handling
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Only initialize if module is enabled
        if (!redco_is_module_enabled('smart-webp-conversion')) {
            return;
        }

        // FIXED: Wait for complete upload including thumbnail generation
        add_action('wp_generate_attachment_metadata', array($this, 'auto_convert_after_complete_upload'), 20, 2);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🚀 ENHANCED WEBP AUTO-CONVERT ENABLED using bulk conversion method after upload');
        }

        // Hook into image serving
        add_filter('wp_get_attachment_image_src', array($this, 'serve_webp_if_supported'), 10, 4);
        add_filter('the_content', array($this, 'replace_images_in_content'), 999);

        // CRITICAL: Hook into Media Library admin interface with higher priority
        add_filter('wp_get_attachment_url', array($this, 'serve_webp_url_in_admin'), 999, 2);
        add_filter('wp_get_attachment_image', array($this, 'serve_webp_image_in_admin'), 999, 5);
        add_filter('attachment_fields_to_edit', array($this, 'add_webp_info_to_media_library'), 10, 2);

        // CRITICAL: Hook into Media Library list table thumbnail generation
        add_filter('wp_get_attachment_thumb_file', array($this, 'serve_webp_thumb_file'), 999, 2);
        add_filter('wp_get_attachment_thumb_url', array($this, 'serve_webp_thumb_url'), 999, 2);

        // CRITICAL: Hook into Media Library table column generation
        add_filter('manage_media_columns', array($this, 'debug_media_columns'), 999);
        add_action('manage_media_custom_column', array($this, 'debug_media_custom_column'), 999, 2);

        // ADDITIONAL: Hook into more specific admin filters
        add_filter('wp_get_attachment_image_src', array($this, 'serve_webp_if_supported'), 999, 4);

        // REMOVED: get_attached_file override causes infinite loops and memory exhaustion
        // Using safer wp_prepare_attachment_for_js approach instead

        // Hook into Media Library JavaScript data
        add_filter('wp_prepare_attachment_for_js', array($this, 'modify_attachment_for_js'), PHP_INT_MAX, 3);

        // Hook into Media Library AJAX endpoint
        add_action('wp_ajax_query-attachments', array($this, 'intercept_media_library_ajax'), 1);
        add_filter('ajax_query_attachments_args', array($this, 'modify_media_query_args'), 10, 1);

        // Add hooks for attachment-related filters
        add_filter('wp_get_attachment_metadata', array($this, 'debug_attachment_metadata'), 999, 2);
        add_action('wp_ajax_query-attachments', array($this, 'debug_ajax_query'), 999);
        add_action('wp_ajax_nopriv_query-attachments', array($this, 'debug_ajax_query'), 999);

        // Register the direct filename fix function if available
        if (function_exists('redco_webp_fix_media_library_filename')) {
            add_filter('wp_prepare_attachment_for_js', 'redco_webp_fix_media_library_filename', PHP_INT_MAX - 1, 3);
        }

        // Add test hook for verification
        add_action('wp_loaded', array($this, 'test_wp_loaded_hook'));

        // Admin-specific hooks
        if (is_admin()) {
            // Force Media Library refresh on admin pages
            add_action('admin_enqueue_scripts', array($this, 'enqueue_media_library_override'));
        }

        // AJAX handlers - REMOVED to prevent conflicts with global handlers
        // The global handlers in class-smart-webp-conversion.php will handle AJAX calls
        // add_action('wp_ajax_redco_webp_enhanced_bulk_convert', array($this, 'ajax_enhanced_bulk_convert'));
        // add_action('wp_ajax_redco_webp_enhanced_test', array($this, 'ajax_enhanced_test'));
        // add_action('wp_ajax_redco_webp_enhanced_stats', array($this, 'ajax_enhanced_stats'));

        // Cleanup hooks
        add_action('wp_scheduled_delete', array($this, 'cleanup_orphaned_webp_files'));
        add_action('delete_attachment', array($this, 'delete_webp_versions'));

        // AJAX handlers for stats refresh
        add_action('wp_ajax_redco_webp_get_recent_conversions', array($this, 'ajax_get_recent_conversions'));
    }

    /**
     * Initialize comprehensive error handling
     */
    private function init_error_handling() {
        // Set custom error handler for WebP operations
        if ($this->settings['enable_logging']) {
            ini_set('log_errors', 1);
            ini_set('error_log', WP_CONTENT_DIR . '/redco-webp-errors.log');
        }
    }

    /**
     * Enhanced logging method
     */
    private function log($message, $level = 'info') {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $prefix = '';
            switch ($level) {
                case 'error':
                    $prefix = '❌ ENHANCED WEBP ERROR: ';
                    break;
                case 'warning':
                    $prefix = '⚠️ ENHANCED WEBP WARNING: ';
                    break;
                case 'info':
                default:
                    $prefix = '🔧 ENHANCED WEBP: ';
                    break;
            }
            error_log($prefix . $message);
        }
    }

    /**
     * Get module settings with enhanced defaults and robust boolean handling
     */
    public function get_settings() {
        // CRITICAL: Use the same settings as the main module for consistency
        $main_settings = get_option('redco_optimizer_smart_webp_conversion', array());

        // CRITICAL DEBUG: Log the raw settings from database
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP SETTINGS DEBUG: Raw main_settings = ' . var_export($main_settings, true));
        }

        // CRITICAL: Robust boolean conversion for auto_convert_uploads
        $auto_convert_uploads = false; // Default to disabled for safety
        if (isset($main_settings['auto_convert_uploads'])) {
            $value = $main_settings['auto_convert_uploads'];
            // Handle various value types: true, 1, '1', 'true', 'on', 'yes'
            if (is_bool($value)) {
                $auto_convert_uploads = $value;
            } elseif (is_numeric($value)) {
                $auto_convert_uploads = (int)$value === 1;
            } elseif (is_string($value)) {
                $auto_convert_uploads = in_array(strtolower($value), ['1', 'true', 'on', 'yes']);
            }
        }

        // Map main module settings to enhanced settings with robust type handling
        $enhanced_settings = array(
            'auto_convert_uploads' => $auto_convert_uploads, // Use robust boolean conversion
            'quality' => isset($main_settings['quality']) ? (int)$main_settings['quality'] : $this->default_settings['quality'],
            'lossless' => isset($main_settings['lossless']) ? (bool)$main_settings['lossless'] : $this->default_settings['lossless'],
            'backup_original' => isset($main_settings['backup_originals']) ? (bool)$main_settings['backup_originals'] : $this->default_settings['backup_original'], // Note: backup_originals -> backup_original
            'replace_in_content' => isset($main_settings['replace_in_content']) ? (bool)$main_settings['replace_in_content'] : true,
            'batch_size' => isset($main_settings['batch_size']) ? (int)$main_settings['batch_size'] : 10,
        );

        // Merge with enhanced defaults
        $settings = wp_parse_args($enhanced_settings, $this->default_settings);

        // CRITICAL DEBUG: Log the final processed settings
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP SETTINGS DEBUG: Final auto_convert_uploads = ' . var_export($settings['auto_convert_uploads'], true));
            error_log('🔧 ENHANCED WEBP SETTINGS DEBUG: Final auto_convert_uploads type = ' . gettype($settings['auto_convert_uploads']));
            error_log('🔧 ENHANCED WEBP SETTINGS DEBUG: Boolean evaluation = ' . ($settings['auto_convert_uploads'] ? 'TRUE' : 'FALSE'));
        }

        return $settings;
    }

    /**
     * Enhanced server capability check
     */
    public function can_convert_webp() {
        static $can_convert = null;

        if ($can_convert !== null) {
            return $can_convert;
        }

        $checks = array(
            'gd_extension' => extension_loaded('gd'),
            'imagewebp_function' => function_exists('imagewebp'),
            'webp_support' => false,
            'memory_available' => $this->check_memory_availability(),
            'write_permissions' => $this->check_write_permissions()
        );

        // Check WebP support in GD
        if ($checks['gd_extension'] && $checks['imagewebp_function']) {
            $gd_info = gd_info();
            $checks['webp_support'] = isset($gd_info['WebP Support']) && $gd_info['WebP Support'];
        }

        $can_convert = $checks['gd_extension'] &&
                      $checks['imagewebp_function'] &&
                      $checks['webp_support'] &&
                      $checks['memory_available'] &&
                      $checks['write_permissions'];

        $this->log('WebP capability check: ' . ($can_convert ? 'PASSED' : 'FAILED'), 'info');
        $this->log('Capability details: ' . json_encode($checks), 'debug');

        return $can_convert;
    }

    /**
     * Check memory availability
     */
    private function check_memory_availability() {
        $memory_limit = $this->get_memory_limit();
        $memory_usage = memory_get_usage(true);
        $available_memory = $memory_limit - $memory_usage;

        // Need at least 64MB available for image processing
        return $available_memory > (64 * 1024 * 1024);
    }

    /**
     * Get memory limit in bytes
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');

        if ($memory_limit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memory_limit, -1));
        $value = (int) $memory_limit;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Check write permissions
     */
    private function check_write_permissions() {
        $upload_dir = redco_safe_wp_upload_dir();
        return is_writable($upload_dir['basedir']);
    }

    /**
     * Enhanced image format detection
     */
    public function is_convertible_image($file_path) {
        if (!$file_path || !is_string($file_path) || !file_exists($file_path)) {
            return false;
        }

        // Check file size
        $file_size = filesize($file_path);
        if ($file_size > self::MAX_FILE_SIZE) {
            $this->log("File too large for conversion: {$file_path} ({$file_size} bytes)", 'warning');
            return false;
        }

        // Get file extension
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Check if extension is supported
        if (!in_array($extension, self::SUPPORTED_FORMATS)) {
            return false;
        }

        // Verify actual image type matches extension
        $image_info = @getimagesize($file_path);
        if (!$image_info) {
            $this->log("Invalid image file: {$file_path}", 'warning');
            return false;
        }

        $mime_type = $image_info['mime'];
        $valid_types = array(
            'image/jpeg',
            'image/png',
            'image/gif'
        );

        return in_array($mime_type, $valid_types);
    }



    /**
     * Enhanced WebP conversion with comprehensive error handling
     */
    public function convert_to_webp($source_path, $custom_settings = null) {
        $this->log("Starting WebP conversion for: {$source_path}", 'info');

        // Validate input
        if (!$this->is_convertible_image($source_path)) {
            throw new Exception("File is not convertible: {$source_path}");
        }

        if (!$this->can_convert_webp()) {
            throw new Exception("WebP conversion not supported on this server");
        }

        // Use custom settings or default
        $settings = $custom_settings ?: $this->settings;

        // Generate WebP path
        $webp_path = $this->get_webp_path($source_path);
        if (!$webp_path) {
            throw new Exception("Could not generate WebP path for: {$source_path}");
        }

        // Check if WebP already exists and is newer
        if (file_exists($webp_path) && filemtime($webp_path) >= filemtime($source_path)) {
            $this->log("WebP already exists and is current: {$webp_path}", 'info');
            return $webp_path;
        }

        // Memory management
        $this->optimize_memory_for_conversion($source_path);

        try {
            // Load source image
            $source_image = $this->load_image($source_path);
            if (!$source_image) {
                throw new Exception("Failed to load source image: {$source_path}");
            }

            // Apply smart quality optimization
            $quality = $this->calculate_smart_quality($source_path, $settings);

            // Resize if needed
            $source_image = $this->resize_if_needed($source_image, $settings);

            // Convert to WebP
            $success = false;
            if ($settings['lossless']) {
                $success = imagewebp($source_image, $webp_path, 100);
            } else {
                $success = imagewebp($source_image, $webp_path, $quality);
            }

            // Clean up memory
            imagedestroy($source_image);

            if (!$success) {
                throw new Exception("imagewebp() function failed");
            }

            if (!file_exists($webp_path)) {
                throw new Exception("WebP file was not created");
            }

            // Verify WebP file integrity
            if (!$this->verify_webp_integrity($webp_path)) {
                @unlink($webp_path);
                throw new Exception("Created WebP file is corrupted");
            }

            // Log success
            $original_size = filesize($source_path);
            $webp_size = filesize($webp_path);
            $savings = $original_size - $webp_size;
            $savings_percent = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;

            $this->log("WebP conversion successful: {$webp_path}", 'info');
            $this->log("Size reduction: {$original_size} → {$webp_size} bytes ({$savings_percent}% savings)", 'info');

            // Update statistics
            $this->update_conversion_stats($original_size, $webp_size);

            return $webp_path;

        } catch (Exception $e) {
            $this->log("WebP conversion failed: " . $e->getMessage(), 'error');

            // Clean up any partial files
            if (file_exists($webp_path)) {
                @unlink($webp_path);
            }

            throw $e;
        }
    }

    /**
     * Load image from file with enhanced error handling
     */
    private function load_image($file_path) {
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            return false;
        }

        $mime_type = $image_info['mime'];

        switch ($mime_type) {
            case 'image/jpeg':
                return @imagecreatefromjpeg($file_path);
            case 'image/png':
                return @imagecreatefrompng($file_path);
            case 'image/gif':
                return @imagecreatefromgif($file_path);
            default:
                return false;
        }
    }

    /**
     * Calculate smart quality based on image characteristics
     */
    private function calculate_smart_quality($file_path, $settings) {
        if (!$settings['smart_quality']) {
            return $settings['quality'];
        }

        $image_info = getimagesize($file_path);
        $file_size = filesize($file_path);

        // Base quality
        $quality = $settings['quality'];

        // Adjust based on file size
        if ($file_size > 2 * 1024 * 1024) { // > 2MB
            $quality -= 5;
        } elseif ($file_size < 100 * 1024) { // < 100KB
            $quality += 5;
        }

        // Adjust based on dimensions
        $pixels = $image_info[0] * $image_info[1];
        if ($pixels > 2000000) { // > 2MP
            $quality -= 5;
        }

        // Ensure quality is within bounds
        return max(30, min(100, $quality));
    }

    /**
     * Resize image if it exceeds maximum dimensions
     */
    private function resize_if_needed($image, $settings) {
        $width = imagesx($image);
        $height = imagesy($image);

        $max_width = $settings['max_width'];
        $max_height = $settings['max_height'];

        if ($width <= $max_width && $height <= $max_height) {
            return $image;
        }

        // Calculate new dimensions maintaining aspect ratio
        $ratio = min($max_width / $width, $max_height / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);

        // Create resized image
        $resized = imagecreatetruecolor($new_width, $new_height);

        // Preserve transparency for PNG
        imagealphablending($resized, false);
        imagesavealpha($resized, true);

        // Resize
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

        // Clean up original
        imagedestroy($image);

        $this->log("Image resized from {$width}x{$height} to {$new_width}x{$new_height}", 'info');

        return $resized;
    }

    /**
     * Verify WebP file integrity
     */
    private function verify_webp_integrity($webp_path) {
        // Check if file exists and has content
        if (!file_exists($webp_path) || filesize($webp_path) < 100) {
            return false;
        }

        // Try to load the WebP file
        $webp_image = @imagecreatefromwebp($webp_path);
        if (!$webp_image) {
            return false;
        }

        imagedestroy($webp_image);
        return true;
    }

    /**
     * Optimize memory for image conversion
     */
    private function optimize_memory_for_conversion($file_path) {
        if (!$this->settings['memory_optimization']) {
            return;
        }

        // Get image dimensions to estimate memory usage
        $image_info = getimagesize($file_path);
        $width = $image_info[0];
        $height = $image_info[1];

        // Estimate memory needed (width * height * 4 bytes per pixel * 3 for processing)
        $estimated_memory = $width * $height * 4 * 3;

        // Check if we have enough memory
        $current_usage = memory_get_usage(true);
        $memory_limit = $this->get_memory_limit();
        $available = $memory_limit - $current_usage;

        if ($estimated_memory > $available * self::MEMORY_THRESHOLD) {
            // Try to increase memory limit
            $new_limit = $current_usage + $estimated_memory + (64 * 1024 * 1024); // Add 64MB buffer
            @ini_set('memory_limit', $new_limit);

            $this->log("Increased memory limit for large image conversion", 'info');
        }

        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    /**
     * Get WebP file path from original path - ROBUST VERSION
     */
    private function get_webp_path($original_path) {

        // Validate input
        if (!$original_path || !is_string($original_path) || empty($original_path)) {
            return false;
        }

        // Use native pathinfo instead of safe wrapper for debugging
        $path_info = pathinfo($original_path);

        if (empty($path_info['dirname']) || empty($path_info['filename'])) {
            return false;
        }

        // CRITICAL FIX: Use forward slash for WordPress compatibility (WordPress normalizes to forward slashes)
        $webp_path = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

        // Normalize path to ensure consistent forward slashes
        $webp_path = wp_normalize_path($webp_path);

        return $webp_path;
    }

    /**
     * CRITICAL: Fix broken WebP path formats from old conversions
     */
    private function fix_webp_path_format($broken_path) {
        // Common broken patterns:
        // D:xampphtdocswordpress/wp-content/uploads/2025/05filename.webp
        // Should be: D:/xampp/htdocs/wordpress/wp-content/uploads/2025/05/filename.webp

        $fixed_path = $broken_path;

        // Fix missing separators after drive letter
        $fixed_path = preg_replace('/^([A-Z]):([^\/\\\\])/', '$1:/$2', $fixed_path);

        // Fix missing separators between directories and filenames
        $fixed_path = preg_replace('/([a-z])([A-Z])/', '$1/$2', $fixed_path);
        $fixed_path = preg_replace('/(\d{2})([A-Za-z])/', '$1/$2', $fixed_path);

        // Normalize the path using WordPress function
        $fixed_path = wp_normalize_path($fixed_path);

        // If the fix didn't work, try to reconstruct from original file
        if (!file_exists($fixed_path)) {
            // Extract filename from broken path
            $filename = basename($broken_path);
            $filename_without_ext = pathinfo($filename, PATHINFO_FILENAME);

            // Try to find the original file and generate correct WebP path
            $upload_dir = wp_upload_dir();
            $year_month = date('Y/m'); // Current year/month

            // Try different possible original file extensions
            $extensions = ['jpg', 'jpeg', 'png', 'gif'];
            foreach ($extensions as $ext) {
                $possible_original = $upload_dir['basedir'] . '/' . $year_month . '/' . $filename_without_ext . '.' . $ext;
                if (file_exists($possible_original)) {
                    $reconstructed_path = $this->get_webp_path($possible_original);
                    return $reconstructed_path;
                }
            }
        }

        return file_exists($fixed_path) ? $fixed_path : false;
    }

    /**
     * Update conversion statistics
     */
    private function update_conversion_stats($original_size, $webp_size) {
        $stats = get_option('redco_webp_enhanced_stats', array(
            'total_conversions' => 0,
            'total_original_size' => 0,
            'total_webp_size' => 0,
            'total_savings' => 0,
            'last_conversion' => 0
        ));

        $stats['total_conversions']++;
        $stats['total_original_size'] += $original_size;
        $stats['total_webp_size'] += $webp_size;
        $stats['total_savings'] += ($original_size - $webp_size);
        $stats['last_conversion'] = time();

        update_option('redco_webp_enhanced_stats', $stats);
    }

    /**
     * COMPATIBILITY: Regular bulk convert method for compatibility with global AJAX handler
     */
    public function ajax_bulk_convert() {
        // Simply call the enhanced version for compatibility
        $this->ajax_enhanced_bulk_convert();
    }

    /**
     * REAL Enhanced bulk conversion with actual WebP file creation
     */
    public function ajax_enhanced_bulk_convert() {
        // Enhanced security and validation checks

        // 1. Verify nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert')) {
            wp_send_json_error(array(
                'message' => 'Security verification failed. Please refresh the page and try again.',
                'error_code' => 'NONCE_FAILED',
                'debug_info' => 'Nonce verification failed for bulk conversion request'
            ));
            return;
        }

        // 2. Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => 'You do not have sufficient permissions to perform this action.',
                'error_code' => 'INSUFFICIENT_PERMISSIONS',
                'debug_info' => 'User lacks manage_options capability'
            ));
            return;
        }

        // 3. Check if WebP module is enabled
        if (!redco_is_module_enabled('smart-webp-conversion')) {
            wp_send_json_error(array(
                'message' => 'WebP conversion module is not enabled. Please enable it in the Modules page.',
                'error_code' => 'MODULE_DISABLED',
                'debug_info' => 'smart-webp-conversion module is not in enabled modules list'
            ));
            return;
        }

        // FIXED: Get parameters with validation, prioritize settings over default
        $batch_size = isset($_POST['batch_size']) ? intval($_POST['batch_size']) : $this->settings['batch_size'];
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        // Validate batch size
        if ($batch_size < 1 || $batch_size > 50) {
            wp_send_json_error(array(
                'message' => 'Invalid batch size. Must be between 1 and 50.',
                'error_code' => 'INVALID_BATCH_SIZE',
                'debug_info' => "Requested batch size: {$batch_size}"
            ));
            return;
        }

        $this->log("Enhanced bulk conversion starting - Batch: {$batch_size}, Offset: {$offset}", 'info');

        // 4. Check server capabilities for WebP conversion
        if (!$this->can_convert_webp()) {
            $gd_info = function_exists('gd_info') ? gd_info() : array();
            $webp_support = isset($gd_info['WebP Support']) ? $gd_info['WebP Support'] : false;

            wp_send_json_error(array(
                'message' => 'Your server does not support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.',
                'error_code' => 'WEBP_NOT_SUPPORTED',
                'debug_info' => array(
                    'gd_extension_loaded' => extension_loaded('gd'),
                    'webp_support' => $webp_support,
                    'gd_info' => $gd_info
                )
            ));
            return;
        }

        try {
            // Get unconverted images with REAL validation
            $images = $this->get_real_unconverted_images($batch_size, $offset);

            if (empty($images)) {
                wp_send_json_success(array(
                    'message' => 'No more images to convert',
                    'processed' => 0,
                    'has_more' => false,
                    'total_processed' => $offset,
                    'debug_info' => 'No images found for conversion'
                ));
                return;
            }

            // FIXED: Proper has_more check by looking ahead for more images
            $has_more = $this->check_has_more_images($offset + $batch_size);

            $results = array(
                'processed' => 0,
                'errors' => array(),
                'conversions' => array(),
                'has_more' => $has_more,
                'debug_info' => array()
            );

            $results['debug_info'][] = "Found " . count($images) . " images to process";
            $results['debug_info'][] = "Has more images: " . ($has_more ? 'YES' : 'NO');

            foreach ($images as $image) {
                try {
                    $file_path = redco_safe_get_attached_file($image->ID);

                    if (!$file_path || !file_exists($file_path)) {
                        $error = "File not found for image ID {$image->ID}: {$file_path}";
                        $results['errors'][] = $error;
                        $results['debug_info'][] = $error;
                        continue;
                    }

                    $results['debug_info'][] = "Processing image ID {$image->ID}: {$file_path}";

                    // REAL WebP conversion with detailed error reporting
                    $webp_result = $this->real_webp_conversion($file_path, $image->ID);

                    if ($webp_result['success']) {
                        $results['conversions'][] = array(
                            'id' => $image->ID,
                            'original_file' => basename($file_path),
                            'webp_file' => basename($webp_result['webp_path']),
                            'original_size' => $webp_result['original_size'],
                            'webp_size' => $webp_result['webp_size'],
                            'savings' => $webp_result['savings']
                        );

                        $results['processed']++;
                        $results['debug_info'][] = "✅ Successfully converted image ID {$image->ID}";

                    } else {
                        $error = "Failed to convert image ID {$image->ID}: {$webp_result['error']}";
                        $results['errors'][] = $error;
                        $results['debug_info'][] = "❌ {$error}";
                    }

                } catch (Exception $e) {
                    $error_msg = "Exception converting image ID {$image->ID}: " . $e->getMessage();
                    $results['errors'][] = $error_msg;
                    $results['debug_info'][] = "❌ {$error_msg}";
                    $this->log($error_msg, 'error');
                }

                // Memory management
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // Return REAL results with detailed debugging
            $error_count = count($results['errors']);
            $message = "REAL conversion completed: {$results['processed']} images processed, {$error_count} errors";

            wp_send_json_success(array(
                'message' => $message,
                'processed' => $results['processed'],
                'errors' => $results['errors'],
                'conversions' => $results['conversions'],
                'has_more' => $results['has_more'],
                'total_processed' => $offset + $results['processed'],
                'debug_info' => $results['debug_info']
            ));

        } catch (Exception $e) {
            $error_msg = "REAL bulk conversion error: " . $e->getMessage();
            $this->log($error_msg, 'error');
            wp_send_json_error($error_msg);
        }
    }

    /**
     * Get REAL unconverted images with proper validation
     */
    private function get_real_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        // Get all image attachments that haven't been converted yet
        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
            LIMIT %d OFFSET %d
        ";

        $images = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));

        // Additional validation - only return images with valid file paths
        $valid_images = array();
        foreach ($images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path) && $this->is_convertible_image($file_path)) {
                $valid_images[] = $image;
            }
        }

        return $valid_images;
    }

    /**
     * FIXED: Simple check if there are more images to process after the given offset
     */
    private function check_has_more_images($next_offset) {
        // Simply try to get one more image at the next offset
        $next_batch = $this->get_real_unconverted_images(1, $next_offset);

        $has_more = !empty($next_batch);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔍 HAS_MORE CHECK: Offset {$next_offset}, Has more: " . ($has_more ? 'YES' : 'NO'));
        }

        return $has_more;
    }

    /**
     * REAL WebP conversion method that actually creates files
     */
    private function real_webp_conversion($file_path, $attachment_id) {
        try {
            // Validate input
            if (!file_exists($file_path)) {
                return array('success' => false, 'error' => 'Source file does not exist');
            }

            if (!$this->is_convertible_image($file_path)) {
                return array('success' => false, 'error' => 'File is not a convertible image format');
            }

            // Get original file size
            $original_size = filesize($file_path);
            if ($original_size === false) {
                return array('success' => false, 'error' => 'Could not get original file size');
            }

            // Generate WebP path
            $webp_path = $this->get_webp_path($file_path);
            if (!$webp_path) {
                return array('success' => false, 'error' => 'Could not generate WebP path');
            }

            // Check if directory is writable
            $webp_dir = dirname($webp_path);
            if (!is_writable($webp_dir)) {
                return array('success' => false, 'error' => "Directory not writable: {$webp_dir}");
            }

            // Load the image
            $source_image = $this->load_image($file_path);
            if (!$source_image) {
                $error = 'Failed to load source image';
                return array('success' => false, 'error' => $error);
            }

            // Convert to WebP with quality settings
            $quality = $this->settings['quality'] ?? 80;

            $success = imagewebp($source_image, $webp_path, $quality);

            // Clean up memory
            imagedestroy($source_image);

            if (!$success) {
                $error = 'imagewebp() function failed';
                return array('success' => false, 'error' => $error);
            }

            // Verify the file was created
            if (!file_exists($webp_path)) {
                $error = 'WebP file was not created on disk';
                return array('success' => false, 'error' => $error);
            }

            $webp_size = filesize($webp_path);
            if ($webp_size === false) {
                return array('success' => false, 'error' => 'Could not get WebP file size');
            }

            // CRITICAL: Convert all WordPress image sizes (thumbnails, medium, large, etc.)
            $webp_sizes = array('full' => $webp_path);
            $total_original_size = $original_size;
            $total_webp_size = $webp_size;

            // Get attachment metadata to find all generated sizes
            $metadata = wp_get_attachment_metadata($attachment_id);
            if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
                $upload_dir = wp_upload_dir();
                $file_dirname = dirname($file_path);



                foreach ($metadata['sizes'] as $size_name => $size_data) {
                    if (!isset($size_data['file']) || empty($size_data['file'])) {
                        continue;
                    }

                    $size_file_path = path_join($file_dirname, $size_data['file']);

                    if (file_exists($size_file_path) && $this->is_convertible_image($size_file_path)) {
                        try {
                            $size_webp_path = $this->get_webp_path($size_file_path);

                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 Converting size '{$size_name}': {$size_file_path} → {$size_webp_path}");
                            }

                            // Load and convert the size image
                            $size_image = $this->load_image($size_file_path);
                            if ($size_image) {
                                $size_success = imagewebp($size_image, $size_webp_path, $quality);
                                imagedestroy($size_image);

                                if ($size_success && file_exists($size_webp_path)) {
                                    $webp_sizes[$size_name] = $size_webp_path;

                                    // Add to total sizes for statistics
                                    $size_original_size = filesize($size_file_path);
                                    $size_webp_size = filesize($size_webp_path);
                                    $total_original_size += $size_original_size;
                                    $total_webp_size += $size_webp_size;

                                    if (defined('WP_DEBUG') && WP_DEBUG) {
                                        error_log("🔥 ✅ Size '{$size_name}' converted successfully");
                                    }

                                    // Delete original size file if backup is disabled
                                    if (!$this->settings['backup_original']) {
                                        unlink($size_file_path);
                                        if (defined('WP_DEBUG') && WP_DEBUG) {
                                            error_log("🔥 🗑️ Deleted original size file: {$size_file_path}");
                                        }
                                    }
                                } else {
                                    if (defined('WP_DEBUG') && WP_DEBUG) {
                                        error_log("🔥 ❌ Failed to convert size '{$size_name}'");
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ❌ Exception converting size '{$size_name}': " . $e->getMessage());
                            }
                        }
                    }
                }
            }

            // Store conversion metadata with all sizes
            $conversion_data = array(
                'converted' => true,
                'conversion_date' => current_time('mysql'),
                'original_size' => $total_original_size,
                'webp_size' => $total_webp_size,
                'quality' => $quality,
                'method' => 'enhanced_bulk_with_sizes',
                'webp_path' => $webp_path,
                'webp_sizes' => $webp_sizes,
                'sizes_converted' => count($webp_sizes)
            );

            update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);

            // CRITICAL FIX: Update WordPress attachment metadata to point to WebP files
            $this->update_attachment_metadata_for_webp($attachment_id, $webp_path, $webp_sizes);

            // CRITICAL: Delete original file if backup is disabled
            if (!$this->settings['backup_original']) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔥 BACKUP DISABLED: Deleting original file: {$file_path}");
                }

                $original_deleted = unlink($file_path);

                if ($original_deleted) {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ORIGINAL FILE DELETED: {$file_path}");
                    }

                    // Update WordPress attachment to point to WebP file
                    update_attached_file($attachment_id, $webp_path);

                    // Update post mime type to WebP
                    wp_update_post(array(
                        'ID' => $attachment_id,
                        'post_mime_type' => 'image/webp'
                    ));

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ATTACHMENT UPDATED: Now points to WebP file");
                    }
                } else {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ERROR: Failed to delete original file: {$file_path}");
                    }
                }
            }

            return array(
                'success' => true,
                'webp_path' => $webp_path,
                'original_size' => $total_original_size,
                'webp_size' => $total_webp_size,
                'savings' => $total_original_size - $total_webp_size,
                'original_deleted' => !$this->settings['backup_original'],
                'sizes_converted' => count($webp_sizes),
                'webp_sizes' => $webp_sizes
            );

        } catch (Exception $e) {
            return array('success' => false, 'error' => 'Exception: ' . $e->getMessage());
        }
    }

    /**
     * Get unconverted images with enhanced filtering
     */
    private function get_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
            LIMIT %d OFFSET %d
        ";

        $images = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));

        // Additional filtering for file existence and convertibility
        $valid_images = array();
        foreach ($images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && $this->is_convertible_image($file_path)) {
                $valid_images[] = $image;
            }
        }

        return $valid_images;
    }

    /**
     * CRITICAL FIX: Update WordPress attachment metadata to point to WebP files
     */
    private function update_attachment_metadata_for_webp($attachment_id, $webp_path, $webp_sizes) {
        // Get current attachment metadata
        $metadata = wp_get_attachment_metadata($attachment_id);

        if (!is_array($metadata)) {
            $metadata = array();
        }

        // Update the main file path to point to WebP
        $upload_dir = wp_upload_dir();
        $webp_relative_path = str_replace($upload_dir['basedir'] . '/', '', $webp_path);

        // Update the file path in metadata
        $metadata['file'] = $webp_relative_path;

        // Update all size entries to point to WebP files
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            foreach ($metadata['sizes'] as $size_name => $size_data) {
                if (isset($webp_sizes[$size_name])) {
                    $webp_size_path = $webp_sizes[$size_name];
                    $webp_size_filename = basename($webp_size_path);

                    // Update the filename to WebP
                    $metadata['sizes'][$size_name]['file'] = $webp_size_filename;

                    // Update mime type
                    $metadata['sizes'][$size_name]['mime-type'] = 'image/webp';
                }
            }
        }

        // Update the main attachment metadata
        wp_update_attachment_metadata($attachment_id, $metadata);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 METADATA FIX: Updated attachment metadata for ID {$attachment_id} to point to WebP files");
        }
    }

    /**
     * CRITICAL FIX: Fix existing converted attachments metadata
     */
    public function fix_existing_webp_metadata() {
        global $wpdb;

        error_log("🔧 METADATA FIX: Starting metadata fix process");

        // Get all attachments that have been converted to WebP
        $converted_attachments = $wpdb->get_results("
            SELECT p.ID
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%converted%'
        ");

        error_log("🔧 METADATA FIX: Found " . count($converted_attachments) . " converted attachments");

        $fixed_count = 0;

        foreach ($converted_attachments as $attachment) {
            $attachment_id = $attachment->ID;
            $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

            if ($conversion_data && isset($conversion_data['webp_path']) && isset($conversion_data['webp_sizes'])) {
                $this->update_attachment_metadata_for_webp(
                    $attachment_id,
                    $conversion_data['webp_path'],
                    $conversion_data['webp_sizes']
                );
                $fixed_count++;
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 METADATA FIX: Fixed metadata for {$fixed_count} existing WebP attachments");
        }

        return $fixed_count;
    }

    /**
     * CRITICAL FIX: Fix existing WebP attachments metadata (run once)
     */
    public function fix_existing_webp_metadata_once() {
        // FORCE RUN: Always run this fix for now to test
        $fixed_count = $this->fix_existing_webp_metadata();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 METADATA FIX: Fix completed for {$fixed_count} attachments");
        }
    }

    /**
     * Enhanced test conversion
     */
    public function ajax_enhanced_test() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            // Test server capabilities
            $capabilities = array(
                'webp_support' => $this->can_convert_webp(),
                'memory_available' => $this->check_memory_availability(),
                'write_permissions' => $this->check_write_permissions(),
                'gd_info' => function_exists('gd_info') ? gd_info() : array()
            );

            // Test with a sample image if available
            $test_result = null;
            $sample_images = $this->get_unconverted_images(1, 0);

            if (!empty($sample_images)) {
                $test_image = $sample_images[0];
                $file_path = redco_safe_get_attached_file($test_image->ID);

                if ($file_path) {
                    try {
                        $webp_path = $this->convert_to_webp($file_path);
                        $test_result = array(
                            'success' => true,
                            'image_id' => $test_image->ID,
                            'original_file' => basename($file_path),
                            'webp_file' => basename($webp_path),
                            'original_size' => filesize($file_path),
                            'webp_size' => filesize($webp_path)
                        );
                    } catch (Exception $e) {
                        $test_result = array(
                            'success' => false,
                            'error' => $e->getMessage()
                        );
                    }
                }
            }

            wp_send_json_success(array(
                'capabilities' => $capabilities,
                'test_conversion' => $test_result,
                'logs' => array_slice($this->errors, -10) // Last 10 log entries
            ));

        } catch (Exception $e) {
            wp_send_json_error('Test failed: ' . $e->getMessage());
        }
    }

    /**
     * Enhanced statistics
     */
    public function ajax_enhanced_stats() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $stats = get_option('redco_webp_enhanced_stats', array(
                'total_conversions' => 0,
                'total_original_size' => 0,
                'total_webp_size' => 0,
                'total_savings' => 0,
                'last_conversion' => 0
            ));

            // Get total image counts
            global $wpdb;

            $total_images = $wpdb->get_var("
                SELECT COUNT(*)
                FROM {$wpdb->posts}
                WHERE post_type = 'attachment'
                AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            ");

            $converted_images = $wpdb->get_var("
                SELECT COUNT(*)
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = 'attachment'
                AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
                AND pm.meta_key = '_webp_conversion_data'
                AND pm.meta_value LIKE '%\"converted\":true%'
            ");

            $unconverted_images = $total_images - $converted_images;

            // Calculate additional metrics
            $compression_ratio = $stats['total_original_size'] > 0 ?
                round((1 - ($stats['total_webp_size'] / $stats['total_original_size'])) * 100, 1) : 0;

            wp_send_json_success(array(
                'total_images' => intval($total_images),
                'converted_images' => intval($converted_images),
                'unconverted_images' => intval($unconverted_images),
                'conversion_percentage' => $total_images > 0 ? round(($converted_images / $total_images) * 100, 1) : 0,
                'total_conversions' => $stats['total_conversions'],
                'total_original_size' => $stats['total_original_size'],
                'total_webp_size' => $stats['total_webp_size'],
                'total_savings' => $stats['total_savings'],
                'compression_ratio' => $compression_ratio,
                'last_conversion' => $stats['last_conversion'],
                'formatted_savings' => size_format($stats['total_savings']),
                'formatted_original_size' => size_format($stats['total_original_size']),
                'formatted_webp_size' => size_format($stats['total_webp_size'])
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get statistics: ' . $e->getMessage());
        }
    }

    /**
     * FIXED: Auto-convert new uploads AFTER complete upload including thumbnails
     */
    public function auto_convert_after_complete_upload($metadata, $attachment_id) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🚀 AUTO-CONVERT: New attachment uploaded, ID: ' . $attachment_id);
        }

        // Get current settings
        $this->settings = $this->get_settings();

        // Check if auto-convert is enabled
        if (!$this->settings['auto_convert_uploads']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: Disabled - skipping conversion for attachment ' . $attachment_id);
            }
            return $metadata;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🚀 AUTO-CONVERT: Enabled - proceeding with conversion for attachment ' . $attachment_id);
        }

        // Get the file path
        $file_path = redco_safe_get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: File not found for attachment ' . $attachment_id . ': ' . $file_path);
            }
            return $metadata;
        }

        // Check if it's a convertible image
        if (!$this->is_convertible_image($file_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: File not convertible for attachment ' . $attachment_id . ': ' . $file_path);
            }
            return $metadata;
        }

        try {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: Starting conversion using bulk method for attachment ' . $attachment_id);
            }

            // Use the WORKING bulk conversion method!
            $conversion_result = $this->real_webp_conversion($file_path, $attachment_id);

            if ($conversion_result['success']) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🚀 AUTO-CONVERT: ✅ SUCCESS! Converted attachment ' . $attachment_id . ' with ' . $conversion_result['sizes_converted'] . ' sizes');
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🚀 AUTO-CONVERT: ❌ FAILED! Error for attachment ' . $attachment_id . ': ' . $conversion_result['error']);
                }
            }

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: ❌ EXCEPTION for attachment ' . $attachment_id . ': ' . $e->getMessage());
            }
        }

        // CRITICAL: Always return metadata to not break WordPress
        return $metadata;
    }

    /**
     * DEPRECATED: Old upload hook method - replaced with auto_convert_new_upload
     */
    public function handle_upload_conversion($upload, $context) {
        // CRITICAL DEBUG: Log all parameters to identify issues
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED handle_upload_conversion called');
            error_log('🔧 ENHANCED Upload parameter: ' . var_export($upload, true));
            error_log('🔧 ENHANCED Context parameter: ' . var_export($context, true));
        }

        // CRITICAL FIX: Comprehensive upload validation (same as regular class)
        if (!is_array($upload)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload is not an array: ' . gettype($upload));
            }
            return $upload;
        }

        if (!isset($upload['file'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload array missing file key');
            }
            return $upload;
        }

        if ($upload['file'] === null || $upload['file'] === '') {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload file is null or empty');
            }
            return $upload;
        }

        if (!is_string($upload['file'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload file is not a string: ' . gettype($upload['file']));
            }
            return $upload;
        }

        // Additional safety check for the file path
        $file_path = $upload['file'];
        if (strpos($file_path, "\0") !== false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload file contains null bytes');
            }
            return $upload;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ ENHANCED Upload file validation passed: ' . $file_path);
        }

        // Get current settings
        $this->settings = $this->get_settings();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 Auto-convert setting: ' . ($this->settings['auto_convert_uploads'] ? 'ENABLED' : 'DISABLED'));
        }

        // SIMPLE LOGIC: Check if auto-convert is enabled
        if (!$this->settings['auto_convert_uploads']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 Auto-convert uploads DISABLED - skipping conversion');
            }
            return $upload;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 Auto-convert uploads ENABLED - proceeding with conversion');
        }

        if (!$this->is_convertible_image($file_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED File is not convertible: ' . $file_path);
            }
            return $upload;
        }

        try {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED Starting WebP conversion for: ' . $file_path);
            }

            $webp_path = $this->convert_to_webp($file_path);
            if ($webp_path && file_exists($webp_path)) {
                // Store conversion info in metadata
                $upload['webp_converted'] = true;
                $upload['webp_path'] = $webp_path;
                $upload['original_size'] = filesize($file_path);
                $upload['webp_size'] = filesize($webp_path);

                $this->log("Upload converted to WebP: " . basename($webp_path), 'info');

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('✅ ENHANCED WebP conversion successful: ' . basename($webp_path));
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('❌ ENHANCED WebP conversion failed or file not created');
                }
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload conversion exception: ' . $e->getMessage());
                error_log('❌ ENHANCED Exception trace: ' . $e->getTraceAsString());
            }
            $this->log("Upload conversion failed: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let exceptions break the upload process
            // Return the original upload array so WordPress can continue
        } catch (Error $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload conversion fatal error: ' . $e->getMessage());
                error_log('❌ ENHANCED Fatal error trace: ' . $e->getTraceAsString());
            }
            $this->log("Upload conversion fatal error: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let fatal errors break the upload process
        }

        return $upload;
    }

    /**
     * Generate WebP versions for attachment metadata
     */
    public function generate_webp_versions($metadata, $attachment_id) {
        // CRITICAL DEBUG: Log the method call
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED generate_webp_versions called for attachment: ' . $attachment_id);
            error_log('🔧 ENHANCED Metadata type: ' . gettype($metadata));
        }

        if (!is_array($metadata)) {
            $metadata = array();
        }

        $file_path = redco_safe_get_attached_file($attachment_id);
        if (!$file_path) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED No file path found for attachment: ' . $attachment_id);
            }
            return $metadata;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED File path for attachment ' . $attachment_id . ': ' . $file_path);
        }

        try {
            if ($this->is_convertible_image($file_path)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED Image is convertible, starting conversion...');
                }

                // Use the enhanced conversion method that converts all sizes
                $conversion_result = $this->real_webp_conversion($file_path, $attachment_id);

                if ($conversion_result['success']) {
                    $this->log("Generated WebP versions for attachment {$attachment_id} with {$conversion_result['sizes_converted']} sizes", 'info');

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('✅ ENHANCED WebP versions generated successfully for attachment: ' . $attachment_id);
                    }
                } else {
                    $this->log("Failed to generate WebP versions for attachment {$attachment_id}: {$conversion_result['error']}", 'error');

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('❌ ENHANCED WebP version generation failed: ' . $conversion_result['error']);
                    }
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED Image is not convertible: ' . $file_path);
                }
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Exception in generate_webp_versions: ' . $e->getMessage());
                error_log('❌ ENHANCED Exception trace: ' . $e->getTraceAsString());
            }
            $this->log("Failed to generate WebP version for attachment {$attachment_id}: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let exceptions break the metadata generation
        } catch (Error $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Fatal error in generate_webp_versions: ' . $e->getMessage());
                error_log('❌ ENHANCED Fatal error trace: ' . $e->getTraceAsString());
            }
            $this->log("Fatal error generating WebP version for attachment {$attachment_id}: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let fatal errors break the metadata generation
        }

        return $metadata;
    }

    /**
     * ENHANCED: Serve WebP images if browser supports it
     */
    public function serve_webp_if_supported($image, $attachment_id, $size, $icon) {
        // Debug logging removed for production

        // In admin area, always serve WebP if available (for thumbnails)
        // On frontend, check browser support
        if (!is_admin() && !$this->browser_supports_webp()) {
            return $image;
        }

        if (!$image) {
            return $image;
        }

        // Check if this image has been converted using enhanced method
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $image;
        }

        // Determine the size key
        $size_key = 'full';
        if (is_string($size)) {
            $size_key = $size;
        } elseif (is_array($size)) {
            $size_key = 'full'; // Custom sizes default to full
        }

        // Get WebP path for the specific size
        $webp_path = '';

        // First, try to get the specific size from webp_sizes
        if (isset($conversion_data['webp_sizes']) && isset($conversion_data['webp_sizes'][$size_key])) {
            $webp_path = $conversion_data['webp_sizes'][$size_key];
        }
        // Fallback to main WebP path for 'full' size
        elseif ($size_key === 'full' && isset($conversion_data['webp_path'])) {
            $webp_path = $conversion_data['webp_path'];
        }
        // Last resort: generate WebP path from original
        else {
            $original_path = redco_safe_get_attached_file($attachment_id);
            if ($original_path) {
                // For specific sizes, try to find the size file
                if ($size_key !== 'full') {
                    $metadata = wp_get_attachment_metadata($attachment_id);
                    if (isset($metadata['sizes'][$size_key]['file'])) {
                        $size_file = path_join(dirname($original_path), $metadata['sizes'][$size_key]['file']);
                        $webp_path = $this->get_webp_path($size_file);
                    }
                } else {
                    $webp_path = $this->get_webp_path($original_path);
                }
            }
        }

        // Check if WebP file exists and serve it
        if ($webp_path && file_exists($webp_path)) {
            // Convert file path to URL
            $upload_dir = wp_upload_dir();
            $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);

            // Normalize path separators for URL
            $webp_url = wp_normalize_path($webp_url);

            // Replace the image URL with WebP URL
            $image[0] = $webp_url;

            // Update width and height if available
            if (file_exists($webp_path)) {
                $webp_info = getimagesize($webp_path);
                if ($webp_info) {
                    $image[1] = $webp_info[0]; // width
                    $image[2] = $webp_info[1]; // height
                }
            }

            // WebP served successfully
        }

        return $image;
    }

    /**
     * ENHANCED: Replace images in content with WebP versions
     */
    public function replace_images_in_content($content) {
        if (!$this->browser_supports_webp()) {
            return $content;
        }

        $settings = $this->get_settings();
        if (!$settings['replace_in_content']) {
            return $content;
        }

        // Use regex to find and replace image URLs
        $pattern = '/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i';
        return preg_replace_callback($pattern, array($this, 'replace_image_callback'), $content);
    }

    /**
     * ENHANCED: Callback for image replacement in content
     */
    private function replace_image_callback($matches) {
        $img_tag = $matches[0];
        $img_url = $matches[1];

        if (!$img_url || !is_string($img_url) || empty($img_url)) {
            return $img_tag;
        }

        // Check if this is a local image
        $upload_dir = wp_upload_dir();
        $base_url = $upload_dir['baseurl'] ?? '';

        if (!$base_url || strpos($img_url, $base_url) === false) {
            return $img_tag;
        }

        // Find corresponding WebP version
        $webp_url = $this->get_webp_url_from_original($img_url);
        if ($webp_url && $webp_url !== $img_url) {
            return str_replace($img_url, $webp_url, $img_tag);
        }

        return $img_tag;
    }

    /**
     * ENHANCED: Get WebP URL from original URL
     */
    private function get_webp_url_from_original($original_url) {
        if (!$original_url || !is_string($original_url) || empty($original_url)) {
            return $original_url;
        }

        $upload_dir = wp_upload_dir();
        $base_url = $upload_dir['baseurl'] ?? '';
        $base_dir = $upload_dir['basedir'] ?? '';

        if (!$base_url || !$base_dir) {
            return $original_url;
        }

        // Convert URL to file path
        $file_path = str_replace($base_url, $base_dir, $original_url);

        // Get WebP path
        $webp_path = $this->get_webp_path($file_path);

        // Check if WebP file exists
        if ($webp_path && file_exists($webp_path)) {
            // Convert back to URL
            return str_replace($base_dir, $base_url, $webp_path);
        }

        return $original_url;
    }

    /**
     * ENHANCED: Check if browser supports WebP
     */
    private function browser_supports_webp() {
        if (!isset($_SERVER['HTTP_ACCEPT'])) {
            return false;
        }

        $http_accept = $_SERVER['HTTP_ACCEPT'];
        if (!$http_accept || !is_string($http_accept)) {
            return false;
        }

        return strpos($http_accept, 'image/webp') !== false;
    }

    /**
     * ENHANCED: Delete WebP versions when attachment is deleted
     */
    public function delete_webp_versions($attachment_id) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 DELETE_WEBP_VERSIONS called for attachment {$attachment_id}");
        }

        // Get conversion data
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['webp_path'])) {
            return;
        }

        $webp_path = $conversion_data['webp_path'];

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 Deleting WebP file: {$webp_path}");
        }

        // Delete the WebP file if it exists
        if ($webp_path && file_exists($webp_path)) {
            $deleted = unlink($webp_path);
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 WebP file deletion " . ($deleted ? 'successful' : 'failed'));
            }
        }

        // Clean up metadata
        delete_post_meta($attachment_id, '_webp_conversion_data');

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 WebP metadata cleaned up for attachment {$attachment_id}");
        }
    }

    /**
     * ENHANCED: Cleanup orphaned WebP files
     */
    public function cleanup_orphaned_webp_files() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 CLEANUP_ORPHANED_WEBP_FILES called");
        }

        // This method can be expanded to clean up orphaned WebP files
        // For now, it's just a placeholder to prevent hook errors
    }

    /**
     * ENHANCED: Serve WebP URL in Media Library admin
     */
    public function serve_webp_url_in_admin($url, $attachment_id) {
        // Debug logging removed for production

        // Only apply in admin interface
        if (!is_admin()) {
            return $url;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $url;
        }

        // Get WebP path and convert to URL
        $webp_path = '';
        if (isset($conversion_data['webp_path']) && $conversion_data['webp_path']) {
            $webp_path = $conversion_data['webp_path'];
        } else {
            // Fallback: generate WebP path from original
            $original_path = redco_safe_get_attached_file($attachment_id);
            if ($original_path) {
                $webp_path = $this->get_webp_path($original_path);
            }
        }

        if ($webp_path && file_exists($webp_path)) {
            $upload_dir = wp_upload_dir();

            // CRITICAL FIX: Normalize paths for Windows compatibility
            $normalized_basedir = wp_normalize_path($upload_dir['basedir']);
            $normalized_webp_path = wp_normalize_path($webp_path);

            $webp_url = str_replace($normalized_basedir, $upload_dir['baseurl'], $normalized_webp_path);

            // Ensure URL uses forward slashes
            $webp_url = str_replace('\\', '/', $webp_url);

            // WebP URL served successfully

            return $webp_url;
        }

        return $url;
    }

    /**
     * ENHANCED: Serve WebP image in Media Library admin
     */
    public function serve_webp_image_in_admin($html, $attachment_id, $size, $icon, $attr) {
        // Only apply in admin interface
        if (!is_admin()) {
            return $html;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $html;
        }

        // Get WebP URL
        $webp_url = $this->serve_webp_url_in_admin(wp_get_attachment_url($attachment_id), $attachment_id);

        if ($webp_url && $webp_url !== wp_get_attachment_url($attachment_id)) {
            // Replace the src attribute with WebP URL
            $html = preg_replace('/src=["\']([^"\']+)["\']/', 'src="' . esc_url($webp_url) . '"', $html);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 ADMIN_IMAGE: Replaced image HTML for attachment {$attachment_id}");
            }
        }

        return $html;
    }

    /**
     * ENHANCED: Serve WebP thumbnail file for Media Library list view
     */
    public function serve_webp_thumb_file($file, $attachment_id) {
        if (!is_admin()) {
            return $file;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $file;
        }

        // Try to get the thumbnail WebP version
        if (isset($conversion_data['webp_sizes']) && isset($conversion_data['webp_sizes']['thumbnail'])) {
            $webp_thumb_path = $conversion_data['webp_sizes']['thumbnail'];
            if (file_exists($webp_thumb_path)) {
                return $webp_thumb_path;
            }
        }

        return $file;
    }

    /**
     * ENHANCED: Serve WebP thumbnail URL for Media Library list view
     */
    public function serve_webp_thumb_url($url, $attachment_id) {
        if (!is_admin()) {
            return $url;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $url;
        }

        // Try to get the thumbnail WebP version
        if (isset($conversion_data['webp_sizes']) && isset($conversion_data['webp_sizes']['thumbnail'])) {
            $webp_thumb_path = $conversion_data['webp_sizes']['thumbnail'];
            if (file_exists($webp_thumb_path)) {
                $upload_dir = wp_upload_dir();

                // CRITICAL FIX: Normalize paths for Windows compatibility
                $normalized_basedir = wp_normalize_path($upload_dir['basedir']);
                $normalized_webp_path = wp_normalize_path($webp_thumb_path);

                $webp_thumb_url = str_replace($normalized_basedir, $upload_dir['baseurl'], $normalized_webp_path);

                // Ensure URL uses forward slashes
                $webp_thumb_url = str_replace('\\', '/', $webp_thumb_url);

                return $webp_thumb_url;
            }
        }

        return $url;
    }

    /**
     * DEBUG: Media Library columns (debug methods removed for production)
     */
    public function debug_media_columns($columns) {
        return $columns;
    }

    /**
     * DEBUG: Media Library custom column content (debug methods removed for production)
     */
    public function debug_media_custom_column($column_name, $attachment_id) {
        // Debug logging removed for production
    }

    /**
     * ENHANCED: Add WebP info to Media Library attachment details
     */
    public function add_webp_info_to_media_library($fields, $post) {
        // Only for image attachments
        if (!wp_attachment_is_image($post->ID)) {
            return $fields;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($post->ID, '_webp_conversion_data', true);

        if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
            $webp_size = isset($conversion_data['webp_size']) ? $conversion_data['webp_size'] : 0;
            $original_size = isset($conversion_data['original_size']) ? $conversion_data['original_size'] : 0;
            $savings = $original_size - $webp_size;
            $savings_percent = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;

            $fields['webp_conversion'] = array(
                'label' => __('WebP Conversion', 'redco-optimizer'),
                'input' => 'html',
                'html' => sprintf(
                    '<div style="background: #e8f5e8; padding: 10px; border-radius: 4px; border-left: 4px solid #4CAF50;">
                        <strong style="color: #2e7d32;">✅ Converted to WebP</strong><br>
                        <small>
                            Original: %s | WebP: %s | Savings: %s (%s%%)
                        </small>
                    </div>',
                    size_format($original_size),
                    size_format($webp_size),
                    size_format($savings),
                    $savings_percent
                ),
                'show_in_edit' => false
            );
        } else {
            $fields['webp_conversion'] = array(
                'label' => __('WebP Conversion', 'redco-optimizer'),
                'input' => 'html',
                'html' => '<div style="background: #fff3cd; padding: 10px; border-radius: 4px; border-left: 4px solid #ffc107;">
                    <strong style="color: #856404;">⏳ Not converted to WebP</strong><br>
                    <small>Use the Smart WebP Conversion module to convert this image.</small>
                </div>',
                'show_in_edit' => false
            );
        }

        return $fields;
    }

    /**
     * CRITICAL: Modify Media Library AJAX response
     */
    public function modify_media_library_ajax() {
        // This runs before the default handler, allowing us to modify the response
    }

    /**
     * CRITICAL: Modify attachment data for JavaScript (Media Library grid)
     */
    public function modify_attachment_for_js($response, $attachment, $meta) {
        // Check if this is our manual test
        if (isset($response['url']) && $response['url'] === 'test') {
            return $response; // Don't process manual tests
        }



        // Only process image attachments
        if (!wp_attachment_is_image($attachment->ID)) {
            return $response;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($attachment->ID, '_webp_conversion_data', true);



        if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
            // Get WebP URL
            $webp_path = '';
            if (isset($conversion_data['webp_path']) && $conversion_data['webp_path']) {
                $webp_path = $conversion_data['webp_path'];
            } else {
                // Fallback: generate WebP path from original
                $original_path = redco_safe_get_attached_file($attachment->ID);
                if ($original_path) {
                    $webp_path = $this->get_webp_path($original_path);
                }
            }

            // TIMING FIX: Check if WebP file exists, if not, try to find it or wait
            // CRITICAL FIX: Try to fix broken stored paths from old conversions
            if ($webp_path && !file_exists($webp_path)) {
                // Try to fix the path format
                $fixed_path = $this->fix_webp_path_format($webp_path);

                if ($fixed_path && file_exists($fixed_path)) {
                    $webp_path = $fixed_path;
                    // Update the stored path in database
                    $conversion_data['webp_path'] = $fixed_path;
                    update_post_meta($attachment->ID, '_webp_conversion_data', $conversion_data);
                }
            }

            if ($webp_path && file_exists($webp_path)) {
                $upload_dir = wp_upload_dir();

                // CRITICAL FIX: Normalize paths for Windows compatibility
                $normalized_basedir = wp_normalize_path($upload_dir['basedir']);
                $normalized_webp_path = wp_normalize_path($webp_path);

                $webp_url = str_replace($normalized_basedir, $upload_dir['baseurl'], $normalized_webp_path);

                // Ensure URL uses forward slashes
                $webp_url = str_replace('\\', '/', $webp_url);


                // AGGRESSIVE: Replace ALL URL references with WebP
                $response['url'] = $webp_url;
                $response['link'] = $webp_url;

                // AGGRESSIVE: Replace ALL filename-related fields to show .webp extension
                if (isset($response['filename'])) {
                    $response['filename'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['filename']);
                }

                // Replace title to indicate WebP
                if (isset($response['title'])) {
                    $response['title'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['title']);
                }

                // Replace name field if it exists
                if (isset($response['name'])) {
                    $response['name'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['name']);
                }

                // Replace all size URLs with their corresponding WebP versions
                if (isset($response['sizes']) && is_array($response['sizes'])) {
                    $upload_dir = wp_upload_dir();

                    foreach ($response['sizes'] as $size => $size_data) {
                        if (isset($size_data['url'])) {
                            $size_webp_url = $webp_url; // Default to main WebP URL

                            // Try to get the specific size WebP URL
                            if (isset($conversion_data['webp_sizes']) && isset($conversion_data['webp_sizes'][$size])) {
                                $size_webp_path = $conversion_data['webp_sizes'][$size];
                                if (file_exists($size_webp_path)) {
                                    // CRITICAL FIX: Normalize paths for Windows compatibility
                                    $normalized_basedir = wp_normalize_path($upload_dir['basedir']);
                                    $normalized_size_path = wp_normalize_path($size_webp_path);

                                    $size_webp_url = str_replace($normalized_basedir, $upload_dir['baseurl'], $normalized_size_path);

                                    // Ensure URL uses forward slashes
                                    $size_webp_url = str_replace('\\', '/', $size_webp_url);
                                }
                            }

                            $response['sizes'][$size]['url'] = $size_webp_url;
                        }
                    }
                }

                // Update icon URL if present
                if (isset($response['icon'])) {
                    $response['icon'] = $webp_url;
                }

                // Add WebP metadata
                $response['webp_converted'] = true;
                $response['webp_original_url'] = wp_get_attachment_url($attachment->ID);
                $response['webp_savings'] = isset($conversion_data['original_size']) && isset($conversion_data['webp_size'])
                    ? $conversion_data['original_size'] - $conversion_data['webp_size']
                    : 0;
                $response['webp_savings_percent'] = isset($conversion_data['original_size']) && $conversion_data['original_size'] > 0
                    ? round((($conversion_data['original_size'] - $conversion_data['webp_size']) / $conversion_data['original_size']) * 100, 1)
                    : 0;


            } else {
                // TIMING FIX: WebP file doesn't exist yet
                // Don't modify the response if WebP doesn't exist yet
                return $response;
            }
        }

        return $response;
    }

    /**
     * SAFE: Enqueue JavaScript to force Media Library to use WebP URLs
     */
    public function enqueue_media_library_override($hook) {
        // Only on Media Library pages
        if ($hook !== 'upload.php' && $hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }

        // WebP Media Library path normalization
        $js = "
        jQuery(document).ready(function($) {
            var fixMediaLibraryDOM = function() {
                var fixedCount = 0;

                // Fix file system paths in DOM elements
                $('*').each(function() {
                    var \$el = $(this);

                    // Fix href attributes
                    var href = \$el.attr('href');
                    if (href && href.match(/^D:\/.*\/wp-content\//)) {
                        var webUrl = href.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                        \$el.attr('href', webUrl);
                        fixedCount++;
                    }

                    // Fix src attributes
                    var src = \$el.attr('src');
                    if (src && src.match(/^D:\/.*\/wp-content\//)) {
                        var webUrl = src.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                        \$el.attr('src', webUrl);
                        fixedCount++;
                    }

                    // Fix text content containing file paths
                    var text = \$el.text();
                    if (text && text.match(/^D:\/.*\/wp-content\//) && \$el.children().length === 0) {
                        var webUrl = text.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                        \$el.text(webUrl);
                        fixedCount++;
                    }

                    // Fix data attributes
                    ['data-src', 'data-url', 'data-href', 'data-original', 'data-full-url'].forEach(function(attr) {
                        var value = \$el.attr(attr);
                        if (value && value.match(/^D:\/.*\/wp-content\//)) {
                            var webUrl = value.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                            \$el.attr(attr, webUrl);
                            fixedCount++;
                        }
                    });
                });

                // Method 2: Specifically target media icons (the ones we found)
                $('.media-icon img').each(function() {
                    var \$img = $(this);
                    var src = \$img.attr('src');

                    if (src && src.match(/^D:\/.*\/wp-content\//)) {
                        var webUrl = src.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                        \$img.attr('src', webUrl);
                        fixedCount++;
                    }
                });

                // FIXED: Only change filename displays for ACTUALLY CONVERTED images

                // Fix filename displays in Media Library interface - ONLY for converted images
                $('.attachment-details .filename, .media-sidebar .filename, .attachment-info .filename, .filename, [class*=\"filename\"], [class*=\"name\"]').each(function() {
                    var \$filename = $(this);
                    var text = \$filename.text();

                    // Handle different filename display formats
                    var cleanText = text ? text.trim().replace(/\\s+/g, ' ') : '';

                    if (cleanText && cleanText.match(/\.(jpg|jpeg|png|gif)$/i)) {
                        // CRITICAL FIX: Only change filename if there's a WebP URL in the same container
                        var \$container = \$filename.closest('.attachment-details, .media-sidebar, .attachment-info, .media-modal');
                        var hasWebPUrl = \$container.find('input, a, img').filter(function() {
                            var val = $(this).val() || $(this).attr('href') || $(this).attr('src') || '';
                            return val.includes('.webp');
                        }).length > 0;

                        if (hasWebPUrl) {
                            var webpFilename = cleanText.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
                            \$filename.text(webpFilename);
                            fixedCount++;
                        }
                    }
                    // Handle File name format - ONLY if WebP exists
                    else if (cleanText && cleanText.indexOf('File name:') !== -1 && cleanText.match(/\\.(jpg|jpeg|png|gif)$/i)) {
                        var \$container = \$filename.closest('.attachment-details, .media-sidebar, .attachment-info, .media-modal');
                        var hasWebPUrl = \$container.find('input, a, img').filter(function() {
                            var val = $(this).val() || $(this).attr('href') || $(this).attr('src') || '';
                            return val.includes('.webp');
                        }).length > 0;

                        if (hasWebPUrl) {
                            var webpText = cleanText.replace(/\\.(jpg|jpeg|png|gif)$/i, '.webp');
                            \$filename.text(webpText);
                            fixedCount++;
                        }
                    }
                });

                // Method 3B: Fix any text content that looks like original filenames - ONLY for converted images
                $('*').each(function() {
                    var \$el = $(this);
                    if (\$el.children().length === 0) { // Only text nodes
                        var text = \$el.text().trim();

                        // Check if it's a filename that should be WebP
                        if (text.match(/^[^\/\\\\]*\.(jpg|jpeg|png|gif)$/i)) {
                            // ENHANCED CHECK: Look for WebP URLs in broader context
                            var \$parent = \$el.closest('.attachment-details, .media-sidebar, .attachment-info, .media-modal, .attachment');
                            var hasWebPUrl = \$parent.find('input, a, img').filter(function() {
                                var val = $(this).val() || $(this).attr('href') || $(this).attr('src') || '';
                                return val.includes('.webp');
                            }).length > 0;

                            if (hasWebPUrl) {
                                var webpFilename = text.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
                                \$el.text(webpFilename);
                                fixedCount++;
                            }
                        }
                    }
                });

                // Method 3C: Fix input field values that contain filenames - ONLY for converted images
                $('input[type=\"text\"]').each(function() {
                    var \$input = $(this);
                    var value = \$input.val();

                    // Check if it's just a filename (not a full URL)
                    if (value && value.match(/^[^\/\\\\]*\.(jpg|jpeg|png|gif)$/i)) {
                        // ENHANCED CHECK: Look for WebP URLs in broader context
                        var \$container = \$input.closest('.attachment-details, .media-sidebar, .attachment-info, .media-modal, .attachment');
                        var hasWebPUrl = \$container.find('input, a, img').filter(function() {
                            var val = $(this).val() || $(this).attr('href') || $(this).attr('src') || '';
                            return val.includes('.webp');
                        }).length > 0;

                        if (hasWebPUrl) {
                            var webpFilename = value.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
                            \$input.val(webpFilename);
                            fixedCount++;
                        }
                    }
                });

                // Fix File URL input fields
                $('input[type=\"text\"], input[type=\"url\"], textarea').each(function() {
                    var \$input = $(this);
                    var value = \$input.val();

                    if (value && value.match(/^D:\/.*\/wp-content\//)) {
                        var webUrl = value.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                        \$input.val(webUrl);
                        fixedCount++;
                    }
                });

                // Fix URL fields by selector
                $('input[name*=\"url\"], input[id*=\"url\"], input[class*=\"url\"]').each(function() {
                    var \$input = $(this);
                    var value = \$input.val();

                    if (value && value.match(/^D:\/.*\/wp-content\//)) {
                        var webUrl = value.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                        \$input.val(webUrl);
                        fixedCount++;
                    }
                });

                // Fix data attributes with file system paths
                $('[data-src], [data-url], [data-href]').each(function() {
                    var \$el = $(this);

                    ['data-src', 'data-url', 'data-href'].forEach(function(attr) {
                        var value = \$el.attr(attr);
                        if (value && value.match(/^D:\/.*\/wp-content\//)) {
                            var webUrl = value.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                            \$el.attr(attr, webUrl);
                            fixedCount++;
                        }
                    });
                });
            };

            // Run fix when page loads
            setTimeout(fixMediaLibraryDOM, 1000);

            // Run fix when content changes (AJAX loads, etc.)
            var observer = new MutationObserver(function(mutations) {
                var shouldFix = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldFix = true;
                    }
                });

                if (shouldFix) {
                    setTimeout(fixMediaLibraryDOM, 500);
                }
            });

            // Observe the media library container
            var mediaContainer = document.querySelector('.wp-list-table, .attachments-browser, #posts-filter');
            if (mediaContainer) {
                observer.observe(mediaContainer, {
                    childList: true,
                    subtree: true
                });
            }

            // Intercept AJAX responses that might contain file system paths
            var originalAjax = $.ajax;
            $.ajax = function(options) {
                var originalSuccess = options.success;

                if (originalSuccess) {
                    options.success = function(data, textStatus, jqXHR) {
                        // Check if response contains file system paths
                        if (data && typeof data === 'object') {
                            // Recursively fix file system paths in response data
                            function fixPathsInObject(obj) {
                                if (typeof obj === 'string' && obj.match(/^D:\/.*\/wp-content\//)) {
                                    var webUrl = obj.replace(/^D:\/.*?\/wp-content\/(.*)$/, window.location.origin + window.location.pathname.replace(/\/wp-admin\/.*/, '') + '/wp-content/$1');
                                    return webUrl;
                                } else if (typeof obj === 'object' && obj !== null) {
                                    for (var key in obj) {
                                        if (obj.hasOwnProperty(key)) {
                                            obj[key] = fixPathsInObject(obj[key]);
                                        }
                                    }
                                }
                                return obj;
                            }

                            data = fixPathsInObject(data);
                        }

                        // Call original success handler with fixed data
                        originalSuccess.call(this, data, textStatus, jqXHR);
                    };
                }

                return originalAjax.call(this, options);
            };

            // Check for inline JavaScript variables with file system paths (removed debug logging)

            // Also run fix periodically as fallback
            setInterval(fixMediaLibraryDOM, 10000);
        });
        ";

        // CRITICAL FIX: Use multiple script handles to ensure JavaScript loads

        // Method 1: Try media-views (WordPress Media Library)
        if (wp_script_is('media-views', 'enqueued') || wp_script_is('media-views', 'registered')) {
            wp_add_inline_script('media-views', $js);
            error_log('🚨 JAVASCRIPT ADDED: Inline script added to media-views');
        }

        // Method 2: Try jquery (always available)
        else if (wp_script_is('jquery', 'enqueued')) {
            wp_add_inline_script('jquery', $js);
            error_log('🚨 JAVASCRIPT ADDED: Inline script added to jquery');
        }

        // Method 3: Direct output in footer as fallback
        else {
            add_action('admin_footer', function() use ($js) {
                echo '<script type="text/javascript">' . $js . '</script>';
                error_log('🚨 JAVASCRIPT ADDED: Direct output in admin_footer');
            });
        }

        // Method 4: Also add to wp_head as additional fallback
        add_action('admin_head', function() use ($js) {
            echo '<script type="text/javascript">' . $js . '</script>';
            error_log('🚨 JAVASCRIPT ADDED: Direct output in admin_head');
        });
    }

    // Test admin notice method removed

    /**
     * TEST: wp_loaded hook to verify ANY hook from our class works
     */
    public function test_wp_loaded_hook() {
        // Silent hook test - no debug output
    }

    /**
     * INVESTIGATION: Debug attachment metadata calls
     */
    public function debug_attachment_metadata($metadata, $attachment_id) {
        error_log('🔍 METADATA HOOK: wp_get_attachment_metadata called for attachment ' . $attachment_id);
        return $metadata;
    }

    /**
     * CRITICAL: Intercept Media Library AJAX requests
     */
    public function intercept_media_library_ajax() {
        error_log('🎯 AJAX INTERCEPT: Media Library AJAX request intercepted');

        // Hook into wp_prepare_attachment_for_js with higher priority for this request
        add_filter('wp_prepare_attachment_for_js', array($this, 'force_webp_in_ajax_response'), PHP_INT_MAX, 3);
        error_log('🎯 AJAX INTERCEPT: Added wp_prepare_attachment_for_js filter for this request');
    }

    /**
     * CRITICAL: Force WebP modifications in AJAX responses
     */
    public function force_webp_in_ajax_response($response, $attachment, $meta) {
        error_log('🎯 AJAX RESPONSE: Forcing WebP modifications for attachment ' . $attachment->ID);

        // Only process image attachments
        if (!wp_attachment_is_image($attachment->ID)) {
            return $response;
        }

        // Check if this image has been converted to WebP
        $conversion_data = get_post_meta($attachment->ID, '_webp_conversion_data', true);

        if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
            error_log('🎯 AJAX RESPONSE: Image is converted, modifying response');

            // Fix broken path if needed
            $webp_path = isset($conversion_data['webp_path']) ? $conversion_data['webp_path'] : '';
            if ($webp_path && !file_exists($webp_path)) {
                $fixed_path = $this->fix_webp_path_format($webp_path);
                if ($fixed_path && file_exists($fixed_path)) {
                    $webp_path = $fixed_path;
                    $conversion_data['webp_path'] = $fixed_path;
                    update_post_meta($attachment->ID, '_webp_conversion_data', $conversion_data);
                    error_log('🎯 AJAX RESPONSE: Fixed broken path: ' . $fixed_path);
                }
            }

            if ($webp_path && file_exists($webp_path)) {
                // Modify filename to show .webp
                if (isset($response['filename'])) {
                    $original_filename = $response['filename'];
                    $response['filename'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $original_filename);
                    error_log('🎯 AJAX RESPONSE: Filename changed: ' . $original_filename . ' → ' . $response['filename']);
                }

                // Modify URL to point to WebP
                $upload_dir = wp_upload_dir();
                $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);
                $response['url'] = $webp_url;
                error_log('🎯 AJAX RESPONSE: URL changed to: ' . $webp_url);

                $response['webp_converted'] = true;
            }
        }

        return $response;
    }

    /**
     * INVESTIGATION: Debug AJAX query-attachments calls
     */
    public function debug_ajax_query() {
        error_log('🎯 REAL AJAX: query-attachments AJAX called');
        error_log('🎯 AJAX POST data: ' . print_r($_POST, true));
        error_log('🎯 AJAX action: ' . ($_POST['action'] ?? 'NO_ACTION'));
    }

    /**
     * INVESTIGATION: Modify media query args
     */
    public function modify_media_query_args($query) {
        error_log('🔍 MEDIA QUERY: ajax_query_attachments_args called');
        return $query;
    }

    /**
     * FIXED: Get WebP conversion statistics compatible with settings page
     */
    public function get_stats() {
        global $wpdb;

        // FIXED: Total images - only count convertible formats (exclude GIF to match conversion logic)
        $total_images = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
        ");

        // FIXED: Get all convertible images and check conversion status programmatically
        $all_images = $wpdb->get_results("
            SELECT p.ID
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
        ");

        $converted_images = 0;
        $total_original_size = 0;
        $total_webp_size = 0;
        $recent_conversions = array();

        foreach ($all_images as $image) {
            $conversion_data = get_post_meta($image->ID, '_webp_conversion_data', true);

            if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
                $converted_images++;

                // Add to totals
                if (isset($conversion_data['original_size'])) {
                    $total_original_size += $conversion_data['original_size'];
                }
                if (isset($conversion_data['webp_size'])) {
                    $total_webp_size += $conversion_data['webp_size'];
                }

                // Add to recent conversions (limit to 10 most recent)
                if (count($recent_conversions) < 10) {
                    $file_path = redco_safe_get_attached_file($image->ID);
                    $recent_conversions[] = array(
                        'id' => $image->ID,
                        'title' => get_the_title($image->ID) ?: 'Untitled',
                        'filename' => $file_path ? basename($file_path) : 'Unknown',
                        'original_size' => $conversion_data['original_size'] ?? 0,
                        'webp_size' => $conversion_data['webp_size'] ?? 0,
                        'savings' => ($conversion_data['original_size'] ?? 0) - ($conversion_data['webp_size'] ?? 0),
                        'conversion_date' => $conversion_data['conversion_date'] ?? current_time('mysql')
                    );
                }
            }
        }

        $total_savings = $total_original_size - $total_webp_size;
        $savings_percentage = $total_original_size > 0 ? round(($total_savings / $total_original_size) * 100, 1) : 0;

        $stats = array(
            'total_images' => intval($total_images),
            'converted_images' => $converted_images,
            'unconverted_images' => intval($total_images) - $converted_images,
            'conversion_percentage' => $total_images > 0 ? round(($converted_images / intval($total_images)) * 100, 1) : 0,
            'total_original_size' => $total_original_size,
            'total_webp_size' => $total_webp_size,
            'total_savings' => $total_savings,
            'savings_percentage' => $savings_percentage,
            'recent_conversions' => $recent_conversions,
            'server_support' => $this->can_convert_webp(),
            'browser_support' => $this->browser_supports_webp()
        );

        return $stats;
    }

    /**
     * Get count of convertible images for dynamic button state
     */
    public function get_convertible_images_count() {
        global $wpdb;

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP: get_convertible_images_count() called');
        }

        // Get all image attachments that haven't been converted yet
        $query = "
            SELECT COUNT(DISTINCT p.ID) as count
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
        ";

        $result = $wpdb->get_var($query);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP: Database query returned: ' . $result . ' potential images');
        }

        // Additional validation - check if files actually exist
        if ($result > 0) {
            $images_query = "
                SELECT p.ID
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
                WHERE p.post_type = 'attachment'
                AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
                AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
                LIMIT 50
            ";

            $images = $wpdb->get_results($images_query);
            $valid_count = 0;

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED WEBP: Checking ' . count($images) . ' images for file existence');
            }

            foreach ($images as $image) {
                $file_path = redco_safe_get_attached_file($image->ID);
                $file_exists = $file_path && file_exists($file_path);
                $is_convertible = $file_exists && $this->is_convertible_image($file_path);

                if (defined('WP_DEBUG') && WP_DEBUG && count($images) <= 5) {
                    // Only log details for first 5 images to avoid spam
                    error_log("🔧 ENHANCED WEBP: Image ID {$image->ID}: file_path={$file_path}, exists={$file_exists}, convertible={$is_convertible}");
                }

                if ($is_convertible) {
                    $valid_count++;
                }
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED WEBP: Found ' . $valid_count . ' valid convertible images out of ' . count($images) . ' checked');
            }

            // If we checked 50 and found valid ones, estimate the total
            if (count($images) == 50 && $valid_count > 0) {
                $ratio = $valid_count / 50;
                $estimated_total = intval($result * $ratio);

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED WEBP: Estimated total: ' . $estimated_total . ' (ratio: ' . $ratio . ')');
                }

                return $estimated_total;
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED WEBP: Returning actual count: ' . $valid_count);
                }
                return $valid_count;
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP: No images found in database - returning 0');
        }

        return 0;
    }

    /**
     * Get recent conversions with enhanced data for the Recent Conversions card
     * FIXED: Properly order by conversion date and limit to 5 items
     */
    public function get_recent_conversions($limit = 5, $offset = 0) {
        global $wpdb;

        // FIXED: Get recent conversions ordered by actual conversion date, not post date
        $query = "
            SELECT p.ID, p.post_title, p.post_date, pm.meta_value as conversion_data,
                   CAST(JSON_EXTRACT(pm.meta_value, '$.conversion_date') AS DATETIME) as conversion_date
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            AND JSON_EXTRACT(pm.meta_value, '$.conversion_date') IS NOT NULL
            ORDER BY conversion_date DESC
            LIMIT %d OFFSET %d
        ";

        $conversions = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));
        $formatted_conversions = array();

        foreach ($conversions as $conversion) {
            $conversion_data = maybe_unserialize($conversion->meta_value);

            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted']) {
                $original_size = $conversion_data['original_size'] ?? 0;
                $webp_size = $conversion_data['webp_size'] ?? 0;
                $savings = $original_size - $webp_size;
                $savings_percentage = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;

                // Get file paths for thumbnail
                $file_path = redco_safe_get_attached_file($conversion->ID);
                $webp_path = $conversion_data['webp_path'] ?? '';

                $formatted_conversions[] = array(
                    'id' => $conversion->ID,
                    'title' => $conversion->post_title ?: 'Untitled',
                    'filename' => $file_path ? basename($file_path) : 'Unknown',
                    'webp_filename' => $webp_path ? basename($webp_path) : 'Unknown',
                    'conversion_date' => $conversion->post_date,
                    'formatted_date' => human_time_diff(strtotime($conversion->post_date), current_time('timestamp')) . ' ago',
                    'original_size' => $original_size,
                    'webp_size' => $webp_size,
                    'savings' => $savings,
                    'savings_percentage' => $savings_percentage,
                    'formatted_original_size' => size_format($original_size),
                    'formatted_webp_size' => size_format($webp_size),
                    'formatted_savings' => size_format($savings),
                    'quality' => $conversion_data['quality'] ?? 80,
                    'method' => $conversion_data['method'] ?? 'standard',
                    'sizes_converted' => $conversion_data['sizes_converted'] ?? 1,
                    'thumbnail_url' => wp_get_attachment_image_url($conversion->ID, 'thumbnail'),
                    'edit_url' => admin_url('post.php?post=' . $conversion->ID . '&action=edit')
                );
            }
        }

        return $formatted_conversions;
    }

    /**
     * Get total count of conversions for pagination
     */
    public function get_conversions_count() {
        global $wpdb;

        $query = "
            SELECT COUNT(p.ID) as count
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
        ";

        return intval($wpdb->get_var($query));
    }

    /**
     * AJAX handler for getting recent conversions
     */
    public function ajax_get_recent_conversions() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        $recent_conversions = $this->get_recent_conversions($limit, $offset);

        wp_send_json_success($recent_conversions);
    }

    // REMOVED: override_attached_file_for_webp method caused infinite loops and memory exhaustion
}

// TARGETED FIX: Since URLs are working, just fix the filename display
function redco_webp_fix_media_library_filename($response, $attachment, $meta) {
    // Only process image attachments
    if (!wp_attachment_is_image($attachment->ID)) {
        return $response;
    }

    // Check if this image has been converted to WebP
    $conversion_data = get_post_meta($attachment->ID, '_webp_conversion_data', true);

    if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
        // AGGRESSIVE: Fix ALL filename-related fields
        if (isset($response['filename'])) {
            $response['filename'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['filename']);
        }

        if (isset($response['title'])) {
            $response['title'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['title']);
        }

        if (isset($response['name'])) {
            $response['name'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['name']);
        }

        // Add WebP indicator
        $response['webp_converted'] = true;
        $response['webp_filename_fixed'] = true;
    }

    return $response;
}

// Register the filename fix hook with MAXIMUM priority
add_filter('wp_prepare_attachment_for_js', 'redco_webp_fix_media_library_filename', PHP_INT_MAX, 3);

// Remove the duplicate initialization - it's now handled in the main file

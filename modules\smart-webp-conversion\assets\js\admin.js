/**
 * Smart WebP Conversion Admin JavaScript
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Script loading verification
window.webpScriptLoaded = true;

// Define global test functions
window.testWebPInit = function() {
    if (typeof window.WebPAdmin !== 'undefined') {
        window.WebPAdmin.init();
    }
};

window.testWebPModal = function() {
    if (typeof window.WebPAdmin !== 'undefined') {
        if (!window.WebPAdmin.initialized) {
            window.WebPAdmin.init();
        }
        window.WebPAdmin.showModal();
    }
};

window.testWebPDebug = function() {
    if (typeof window.WebPAdmin !== 'undefined') {
        if (!window.WebPAdmin.initialized) {
            window.WebPAdmin.init();
        }
        window.WebPAdmin.debugImageDetection();
    }
};

(function($) {
    'use strict';

    var WebPAdmin = {

        // Properties
        isConverting: false,
        conversionData: {
            totalImages: 0,
            processedImages: 0,
            errorCount: 0,
            totalSavings: 0,
            currentBatch: 0,
            batchSize: 10
        },

        // Initialize
        init: function() {
            if (this.initialized) {
                return;
            }

            this.initProgressModal();
            this.bindEvents();
            this.updateStats();
            this.initialized = true;
        },

        // Initialize progress modal
        initProgressModal: function() {
            const existingModal = $('#webp-progress-modal');

            // Create progress modal HTML if it doesn't exist
            if (existingModal.length === 0) {
                const modalHtml = `
                    <div id="webp-progress-modal" class="redco-modal" style="display: none;">
                        <div class="redco-modal-content compact-modal">
                            <div class="redco-modal-header">
                                <h3 id="webp-progress-modal-title">Converting Images to WebP</h3>
                            </div>
                            <div class="redco-modal-body">
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-stats left-aligned">
                                        <span class="progress-current">0</span> / <span class="progress-total">0</span> images processed
                                    </div>
                                </div>
                                <div class="conversion-stats single-row">
                                    <div class="stat-card">
                                        <span class="stat-label">Processed</span>
                                        <span class="stat-value processed-count">0</span>
                                    </div>
                                    <div class="stat-card">
                                        <span class="stat-label">Errors</span>
                                        <span class="stat-value error-count">0</span>
                                    </div>
                                    <div class="stat-card">
                                        <span class="stat-label">Savings</span>
                                        <span class="stat-value total-savings">0 KB</span>
                                    </div>
                                </div>
                                <div class="current-operation">
                                    <div class="current-file">Preparing conversion...</div>
                                </div>
                                <div class="conversion-log-section">
                                    <div class="log-toggle" id="log-toggle">
                                        <span class="dashicons dashicons-arrow-right-alt2"></span>
                                        <span class="log-toggle-text">Conversion Log</span>
                                        <span class="log-count">(0 entries)</span>
                                    </div>
                                    <div class="conversion-log collapsed" id="conversion-log">
                                        <div class="log-entries"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="redco-modal-footer">
                                <button type="button" class="button button-secondary" id="cancel-conversion">Cancel</button>
                                <button type="button" class="button button-primary" id="close-conversion-modal" style="display: none;">Close</button>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(modalHtml);

                // Bind collapsible log toggle
                this.bindLogToggle();
            }
        },

        // Bind collapsible log toggle functionality
        bindLogToggle: function() {
            $(document).on('click', '#log-toggle', function() {
                const $toggle = $(this);
                const $log = $('#conversion-log');
                const $icon = $toggle.find('.dashicons');

                if ($log.hasClass('collapsed')) {
                    // Expand log
                    $log.removeClass('collapsed').addClass('expanded');
                    $icon.removeClass('dashicons-arrow-right-alt2').addClass('dashicons-arrow-down-alt2');
                } else {
                    // Collapse log
                    $log.removeClass('expanded').addClass('collapsed');
                    $icon.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-right-alt2');
                }
            });
        },

        // Update log entry count
        updateLogCount: function() {
            const entryCount = $('.log-entries .log-entry').length;
            $('.log-count').text(`(${entryCount} entries)`);
        },

        // Bind event handlers
        bindEvents: function() {
            // Use event delegation for better compatibility
            $(document).on('click', '#bulk-convert-images, #header-bulk-convert-images', function(e) {
                e.preventDefault();

                const $button = $(this);

                // Check if button is disabled
                if ($button.prop('disabled')) {
                    if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.error('WebP conversion is not available. Your server may not support WebP conversion.', {
                            title: 'WebP Not Supported',
                            duration: 6000
                        });
                    } else {
                        alert('WebP conversion is not available. Your server may not support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.');
                    }
                    return;
                }

                WebPAdmin.startBulkConversion();
            });

            // Test modal button
            $(document).on('click', '#test-webp-modal', function(e) {
                e.preventDefault();
                WebPAdmin.showModal();
            });

            // Debug image detection button
            $(document).on('click', '#debug-image-detection', function(e) {
                e.preventDefault();
                WebPAdmin.debugImageDetection();
            });

            // Simple AJAX test button
            $(document).on('click', '#test-simple-ajax', function(e) {
                e.preventDefault();
                WebPAdmin.testSimpleAjax();
            });

            // Test image detection button
            $(document).on('click', '#test-image-detection', function(e) {
                e.preventDefault();
                WebPAdmin.testImageDetection();
            });

            // Verify WebP files button
            $(document).on('click', '#verify-webp-files', function(e) {
                e.preventDefault();
                WebPAdmin.verifyWebPFiles();
            });

            // Reset WebP conversions button
            $(document).on('click', '#reset-webp-conversions', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to reset all WebP conversion data? This will allow images to be re-converted with new settings.')) {
                    WebPAdmin.resetWebPConversions();
                }
            });

            // Reset specific images button
            $(document).on('click', '#reset-specific-images', function(e) {
                e.preventDefault();
                WebPAdmin.resetSpecificImages();
            });

            // Test WebP creation button
            $(document).on('click', '#test-webp-creation', function(e) {
                e.preventDefault();
                WebPAdmin.testWebPCreation();
            });

            // Check Image Status button
            $(document).on('click', '#check-image-status', function(e) {
                e.preventDefault();
                WebPAdmin.checkImageStatus();
            });

            // Enhanced WebP Test button
            $(document).on('click', '#test-enhanced-webp', function(e) {
                e.preventDefault();
                WebPAdmin.testEnhancedWebP();
            });

            // Test server support button
            $('#test-webp-support').on('click', this.testServerSupport.bind(this));

            // Modal close buttons
            $('#close-conversion-modal, #cancel-conversion').on('click', this.closeModal.bind(this));

            // Quality slider - update display only, auto-save is handled globally
            $('#quality').on('input', function() {
                $('#quality-value').text($(this).val());
            });

            // Lossless toggle
            $('#lossless').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#quality-setting').slideUp();
                } else {
                    $('#quality-setting').slideDown();
                }
            });

            // WebP checkbox change handlers - use event delegation to avoid conflicts with auto-save
            $(document).on('change', '.webp-checkbox', this.updateWebPSummary.bind(this));

            // Auto-refresh stats every 30 seconds
            setInterval(this.updateStats.bind(this), 30000);

            // Initialize summary
            this.updateWebPSummary();
        },

        // Start bulk conversion process (now using enhanced functionality)
        startBulkConversion: function() {
            if (this.isConverting) {
                alert('Conversion already in progress!');
                return;
            }

            // Show modal
            this.showModal();
            this.isConverting = true;

            // FIXED: Get batch size from settings instead of hardcoded value
            const batchSize = this.getBatchSizeFromSettings();

            // Reset conversion data with enhanced settings
            this.conversionData = {
                totalImages: 0,
                processedImages: 0,
                errorCount: 0,
                totalSavings: 0,
                currentBatch: 0,
                batchSize: batchSize
            };

            console.log('🔧 Starting bulk conversion with batch size:', batchSize);

            // Update modal title
            $('#webp-progress-modal-title').text('WebP Conversion');
            $('.current-file').text('Starting bulk conversion...');

            // Start enhanced processing
            this.processEnhancedBatch(0);
        },

        // FIXED: Get batch size from settings
        getBatchSizeFromSettings: function() {
            // Try to get from settings form first
            const settingsBatchSize = $('#batch_size').val();
            if (settingsBatchSize) {
                const size = parseInt(settingsBatchSize);
                if (size >= 5 && size <= 50) {
                    console.log('🔧 Using batch size from settings:', size);
                    return size;
                }
            }

            // Fallback to default
            console.log('🔧 Using default batch size: 10');
            return 10;
        },

        // Process Enhanced Batch (now the main conversion method)
        processEnhancedBatch: function(offset) {
            const self = this;

            $('.current-file').text(`Processing batch ${Math.floor(offset / self.conversionData.batchSize) + 1}...`);

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    batch_size: self.conversionData.batchSize,
                    offset: offset,
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success) {
                        self.handleEnhancedBatchSuccess(response);
                    } else {
                        // Enhanced error handling with specific error codes
                        let errorMsg = 'Conversion failed';
                        let debugInfo = '';

                        if (response.data) {
                            if (response.data.message) {
                                errorMsg = response.data.message;
                            }

                            if (response.data.error_code) {
                                switch (response.data.error_code) {
                                    case 'NONCE_FAILED':
                                        errorMsg = 'Security verification failed. Please refresh the page and try again.';
                                        break;
                                    case 'INSUFFICIENT_PERMISSIONS':
                                        errorMsg = 'You do not have sufficient permissions to perform this action.';
                                        break;
                                    case 'MODULE_DISABLED':
                                        errorMsg = 'WebP conversion module is not enabled. Please enable it in the Modules page.';
                                        break;
                                    case 'WEBP_NOT_SUPPORTED':
                                        errorMsg = 'Your server does not support WebP conversion. Please contact your hosting provider.';
                                        break;
                                    case 'INVALID_BATCH_SIZE':
                                        errorMsg = 'Invalid batch size. Please refresh the page and try again.';
                                        break;
                                }
                            }

                            if (response.data.debug_info) {
                                debugInfo = response.data.debug_info;
                                console.error('WebP Conversion Debug Info:', debugInfo);
                            }
                        }

                        self.handleError(errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    let errorMsg = 'Network error during conversion';
                    let debugInfo = '';

                    // Enhanced network error handling
                    if (xhr.status === 400) {
                        errorMsg = 'Bad request. Please check your settings and try again.';
                        debugInfo = 'HTTP 400: The request was malformed or invalid.';
                    } else if (xhr.status === 403) {
                        errorMsg = 'Access denied. Please check your permissions.';
                        debugInfo = 'HTTP 403: Insufficient permissions for this action.';
                    } else if (xhr.status === 404) {
                        errorMsg = 'Service not found. Please refresh the page and try again.';
                        debugInfo = 'HTTP 404: AJAX endpoint not found.';
                    } else if (xhr.status === 500) {
                        errorMsg = 'Server error. Please try again or contact support.';
                        debugInfo = 'HTTP 500: Internal server error occurred.';
                    } else if (xhr.status === 0) {
                        errorMsg = 'Connection failed. Please check your internet connection.';
                        debugInfo = 'Network connection failed or request was aborted.';
                    } else {
                        errorMsg = `Network error (${xhr.status}). Please try again.`;
                        debugInfo = `HTTP ${xhr.status}: ${xhr.statusText}`;
                    }

                    console.error('WebP Conversion Network Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error,
                        debugInfo: debugInfo
                    });

                    self.handleError(errorMsg);
                }
            });
        },

        // Handle successful enhanced batch conversion
        handleEnhancedBatchSuccess: function(response) {
            const data = response.data;

            // Update conversion data with real results
            this.conversionData.processedImages = data.total_processed;
            this.conversionData.errorCount += (data.errors ? data.errors.length : 0);

            // Calculate total savings from real conversions
            const batchSavings = (data.conversions || []).reduce((total, conversion) => {
                return total + (conversion.savings || 0);
            }, 0);
            this.conversionData.totalSavings += batchSavings;

            // Update progress with real data
            this.updateEnhancedProgress(data);

            // Log real conversions and errors
            this.logEnhancedConversions(data.conversions || [], data.errors || []);

            // Log debug info if available
            if (data.debug_info && data.debug_info.length > 0) {
                this.logDebugInfo(data.debug_info);
            }

            // ENHANCED DEBUG: Log decision logic
            console.log('🔍 BATCH DECISION: has_more =', data.has_more, ', isConverting =', this.isConverting);
            console.log('🔍 BATCH DECISION: total_processed =', data.total_processed, ', processed =', data.processed);

            // Continue with next batch if there are more images
            if (data.has_more && this.isConverting) {
                console.log('🔄 CONTINUING: More images to process, scheduling next batch...');
                setTimeout(() => this.processEnhancedBatch(data.total_processed), 1500);
            } else {
                console.log('🏁 COMPLETING: No more images or conversion stopped');
                console.log('🔍 Reason: has_more =', data.has_more, ', isConverting =', this.isConverting);
                this.completeEnhancedConversion();
            }
        },

        // Update enhanced progress display
        updateEnhancedProgress: function(data) {
            const processed = data.total_processed;
            const total = data.total_images || processed;

            // Update progress bar with real data
            const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            $('.progress-fill').css('width', percentage + '%');

            // Update progress text with real numbers
            $('.progress-current').text(processed);
            $('.progress-total').text(total);

            // Update stats with real data
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));

            // Update current file being processed
            if (data.current_file) {
                $('.current-file').text(`Processing: ${data.current_file}`);
            }
        },

        // Log enhanced conversion results with detailed information
        logEnhancedConversions: function(conversions, errors) {
            const logContainer = $('.log-entries');

            // Safety check for log container
            if (logContainer.length === 0) {
                return;
            }

            // Log successful conversions with detailed info
            conversions.forEach((conversion) => {
                const logEntry = $('<div class="log-entry log-success">')
                    .html(`<span class="dashicons dashicons-yes"></span> ` +
                          `<strong>${conversion.title}</strong><br>` +
                          `<small>Original: ${this.formatFileSize(conversion.original_size)} → ` +
                          `WebP: ${this.formatFileSize(conversion.webp_size)} ` +
                          `(${conversion.savings_percentage}% saved)</small>`);
                logContainer.append(logEntry);
            });

            // Log errors with detailed info
            errors.forEach((error) => {
                const logEntry = $('<div class="log-entry log-error">')
                    .html(`<span class="dashicons dashicons-no"></span> ` +
                          `<strong>${error.title}</strong><br>` +
                          `<small>Error: ${error.error}</small>`);
                logContainer.append(logEntry);
            });

            // Scroll to bottom (with safety check)
            if (logContainer[0]) {
                logContainer.scrollTop(logContainer[0].scrollHeight);
            }

            // Update log count
            this.updateLogCount();
        },

        // Log debug information
        logDebugInfo: function(debugInfo) {
            const logContainer = $('.log-entries');
            if (logContainer.length === 0) return;

            debugInfo.forEach((info) => {
                const logEntry = $('<div class="log-entry log-debug">')
                    .html(`<span class="dashicons dashicons-info"></span> ` +
                          `<small>${info}</small>`);
                logContainer.append(logEntry);
            });

            // Scroll to bottom
            if (logContainer[0]) {
                logContainer.scrollTop(logContainer[0].scrollHeight);
            }

            // Update log count
            this.updateLogCount();
        },

        // Complete enhanced conversion process
        completeEnhancedConversion: function() {
            this.isConverting = false;

            // Update modal title
            $('#webp-progress-modal-title').text('WebP Conversion Complete');
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // CRITICAL: Update all stats and UI elements with fresh data
            console.log('🔄 Bulk conversion complete - refreshing all statistics...');
            this.updateStats();

            // Show detailed completion message
            const processed = this.conversionData.processedImages;
            const errors = this.conversionData.errorCount;
            const savings = this.conversionData.totalSavings;

            let message = `<strong>✅ Conversion Complete!</strong><br>`;
            message += `📊 Processed: ${processed} images<br>`;

            if (savings > 0) {
                message += `💾 Total Savings: ${this.formatFileSize(savings)}<br>`;
            }

            if (errors > 0) {
                message += `⚠️ Errors: ${errors} images failed<br>`;
            }

            message += `<br><em>Check the log above for detailed results.</em>`;

            $('.current-file').html(message);

            // Add completion log entry
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const completionEntry = $('<div class="log-entry log-complete">')
                    .html(`<span class="dashicons dashicons-yes-alt"></span> ` +
                          `<strong>Bulk conversion completed successfully!</strong>`);
                logContainer.append(completionEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Trigger completion event for enhanced features
            $(document).trigger('webp-conversion-completed', {
                summary: {
                    total_processed: processed,
                    successful: processed - errors,
                    failed: errors,
                    total_savings: this.formatFileSize(savings),
                    average_savings: this.conversionData.averageSavings || '0%'
                },
                timestamp: new Date().toISOString()
            });

            // Show toast notification
            if (typeof RedcoToast !== 'undefined') {
                RedcoToast.success(`Successfully converted ${processed} images!`, {
                    title: 'Conversion Complete',
                    duration: 8000,
                    action: {
                        text: 'View Details',
                        callback: function() {
                            // FIXED: Scroll to Recent Conversions section and highlight it
                            const $recentSection = $('.recent-conversions-card');
                            if ($recentSection.length > 0) {
                                // Scroll to the section
                                $('html, body').animate({
                                    scrollTop: $recentSection.offset().top - 100
                                }, 500);

                                // Add highlight effect
                                $recentSection.addClass('highlight-flash');
                                setTimeout(() => {
                                    $recentSection.removeClass('highlight-flash');
                                }, 2000);

                                // Refresh recent conversions to show latest
                                WebPAdmin.loadRecentConversions();
                            }
                        },
                        dismissOnClick: false
                    }
                });
            }
        },

        // Test server support
        testServerSupport: function() {
            var button = $('#test-webp-support');
            var originalText = button.text();

            button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    button.prop('disabled', false).text(originalText);

                    if (response.success) {
                        alert('✓ ' + response.message);
                    } else {
                        alert('✗ ' + response.message);
                    }

                    // Server capabilities tested
                },
                error: function() {
                    button.prop('disabled', false).text(originalText);
                    alert('Error testing server support');
                }
            });
        },

        // ENHANCED: Update all statistics and UI elements after bulk conversion
        updateStats: function() {
            console.log('🔄 Updating all WebP statistics and UI elements...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success !== false && response.data) {
                        const data = response.data;
                        console.log('📊 Stats data received:', data);

                        // 1. Update header metrics (main dashboard cards)
                        $('.header-metric').eq(0).find('.header-metric-value').text((data.converted_images || 0).toLocaleString());
                        $('.header-metric').eq(1).find('.header-metric-value').text((data.conversion_percentage || 0) + '%');
                        $('.header-metric').eq(2).find('.header-metric-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));

                        // 2. Update sidebar statistics
                        $('.stat-item.stat-total .stat-value').text((data.total_images || 0).toLocaleString());
                        $('.stat-item.stat-converted .stat-value').text((data.converted_images || 0).toLocaleString());
                        $('.stat-item.stat-savings .stat-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));
                        $('.stat-item.stat-percentage .stat-value').text((data.savings_percentage || 0) + '%');

                        // 3. Update conversion button state
                        WebPAdmin.updateConvertButtonState();

                        // 4. Refresh recent conversions list
                        WebPAdmin.loadRecentConversions();

                        // 5. Update any progress indicators
                        if (data.conversion_percentage !== undefined) {
                            $('.conversion-progress').css('width', data.conversion_percentage + '%');
                        }

                        // 6. Show success notification
                        if (typeof RedcoToast !== 'undefined') {
                            RedcoToast.success('Statistics updated successfully!', {
                                title: 'Stats Refreshed',
                                duration: 3000
                            });
                        }

                        console.log('✅ All statistics updated successfully');
                    } else {
                        console.warn('⚠️ Invalid stats response:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Stats update failed:', xhr.status, xhr.statusText);

                    // Show user-friendly error for critical failures
                    if (xhr.status >= 500) {
                        if (typeof RedcoToast !== 'undefined') {
                            RedcoToast.error('Failed to refresh statistics. Please reload the page.', {
                                title: 'Update Error',
                                duration: 5000
                            });
                        }
                    }
                }
            });
        },

        // Update convert button state based on available images
        updateConvertButtonState: function() {
            console.log('🔄 Updating convert button state...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success && response.data) {
                        const count = response.data.convertible_count || 0;
                        const $button = $('#bulk-convert-images');
                        const $buttonText = $('#convert-button-text');
                        const $spinner = $('#convert-button-spinner');

                        // Hide spinner
                        $spinner.hide();

                        if (count > 0) {
                            $button.prop('disabled', false);
                            $buttonText.text(`Convert ${count} Images`);
                            console.log(`✅ Button enabled for ${count} images`);
                        } else {
                            $button.prop('disabled', true);
                            $buttonText.text('No Images to Convert');
                            console.log('ℹ️ Button disabled - no convertible images');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Failed to update button state:', error);
                    // Keep button in safe state
                    $('#bulk-convert-images').prop('disabled', true);
                    $('#convert-button-text').text('Error Checking Images');
                    $('#convert-button-spinner').hide();
                }
            });
        },

        // Load recent conversions list
        loadRecentConversions: function() {
            console.log('🔄 Loading recent conversions...');

            const $loadingDiv = $('#conversions-loading');
            const $listDiv = $('#recent-conversions-list');
            const $emptyDiv = $('#conversions-empty');

            // Show loading state
            $loadingDiv.show();
            $listDiv.hide();
            $emptyDiv.hide();

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_recent_conversions',
                    limit: 5, // FIXED: Show maximum 5 recent conversions
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    $loadingDiv.hide();

                    if (response.success && response.data && response.data.length > 0) {
                        // Build conversions list HTML
                        let html = '';
                        response.data.forEach(function(conversion) {
                            html += `
                                <div class="conversion-item">
                                    <div class="conversion-info">
                                        <strong>${conversion.title}</strong>
                                        <div class="conversion-details">
                                            <span class="file-size">${conversion.formatted_original_size} → ${conversion.formatted_webp_size}</span>
                                            <span class="savings">${conversion.savings_percentage}% saved</span>
                                        </div>
                                        <div class="conversion-date">${conversion.formatted_date}</div>
                                    </div>
                                </div>
                            `;
                        });

                        $listDiv.html(html).show();
                        console.log(`✅ Loaded ${response.data.length} recent conversions`);
                    } else {
                        $emptyDiv.show();
                        console.log('ℹ️ No recent conversions found');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Failed to load recent conversions:', error);
                    $loadingDiv.hide();
                    $emptyDiv.show();
                }
            });
        },

        // Show conversion modal (following working Diagnostic pattern)
        showModal: function() {
            console.log('🔧 WebP showModal called');

            // Ensure modal exists first
            this.initProgressModal();

            const $modal = $('#webp-progress-modal');
            console.log('🔧 Modal element found:', $modal.length);

            if ($modal.length === 0) {
                console.error('❌ Modal element still not found after creation');
                alert('Modal creation failed! Check console for details.');
                return;
            }

            console.log('🔧 Showing modal...');
            $('#webp-progress-modal-title').text('Converting Images to WebP');
            $modal.show();
            $('#close-conversion-modal').hide();

            // Reset modal content
            $('.progress-fill').css('width', '0%');
            $('.progress-current').text('0');
            $('.progress-total').text('0');
            $('.processed-count').text('0');
            $('.error-count').text('0');
            $('.total-savings').text('0 KB');
            $('.log-entries').empty();
            $('.current-file').text('Preparing conversion...');

            // Reset log section to collapsed state
            $('#conversion-log').removeClass('expanded').addClass('collapsed');
            $('#log-toggle .dashicons').removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-right-alt2');
            $('.log-count').text('(0 entries)');

            // Show/hide buttons
            $('#cancel-conversion').show();

            console.log('✅ Modal should now be visible');
        },

        // Debug image detection
        debugImageDetection: function() {
            console.log('🔧 Testing image detection...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success) {
                        alert('Debug Results:\n' +
                              'Total Images: ' + response.data.total_images + '\n' +
                              'Image IDs: ' + response.data.image_ids.join(', ') + '\n' +
                              'MIME Types: ' + response.data.mime_types.join(', '));
                    } else {
                        alert('Debug failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {

                    alert('Debug request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test simple AJAX (no nonce required)
        testSimpleAjax: function() {
            console.log('🔧 Testing simple AJAX...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_simple'
                },
                success: function(response) {
                    console.log('✅ Simple AJAX response:', response);
                    alert('Simple AJAX Test Result:\n' + response.data.message);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Simple AJAX failed:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                    alert('Simple AJAX failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Verify WebP files exist on disk
        verifyWebPFiles: function() {
            console.log('🔧 Verifying WebP files on disk...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_verify_files',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP file verification response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP File Verification Results:\n\n';
                        message += 'Database Records: ' + data.database_records + '\n';
                        message += 'Files Found on Disk: ' + data.files_found + '\n';
                        message += 'Missing Files: ' + data.missing_files + '\n';
                        message += 'Empty Files: ' + data.empty_files + '\n\n';

                        if (data.missing_file_list && data.missing_file_list.length > 0) {
                            message += 'Missing Files:\n';
                            data.missing_file_list.forEach(function(file) {
                                message += '- ' + file + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Verification failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP verification failed:', xhr.status, xhr.statusText);
                    alert('Verification request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Reset WebP conversion data
        resetWebPConversions: function() {
            console.log('🔧 Resetting WebP conversion data...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_reset_conversions',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP reset response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP Conversion Reset Results:\n\n';
                        message += 'Database Records Cleared: ' + data.records_cleared + '\n';
                        message += 'WebP Files Deleted: ' + data.files_deleted + '\n';
                        message += 'Conversion Log Cleared: ' + (data.log_cleared ? 'Yes' : 'No') + '\n\n';
                        message += 'You can now re-convert images with new settings.';

                        alert(message);

                        // Refresh the page to update statistics
                        location.reload();
                    } else {
                        alert('Reset failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP reset failed:', xhr.status, xhr.statusText);
                    alert('Reset request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test WebP creation with actual file
        testWebPCreation: function() {
            console.log('🔧 Testing WebP creation with actual file...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_creation',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP creation test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP Creation Test Results:\n\n';
                        message += 'Test Image Found: ' + (data.test_image_found ? 'Yes' : 'No') + '\n';
                        message += 'Test Image Path: ' + (data.test_image_path || 'N/A') + '\n';
                        message += 'WebP Support: ' + (data.webp_support ? 'Yes' : 'No') + '\n';
                        message += 'Directory Writable: ' + (data.directory_writable ? 'Yes' : 'No') + '\n';
                        message += 'Conversion Attempted: ' + (data.conversion_attempted ? 'Yes' : 'No') + '\n';
                        message += 'imagewebp() Success: ' + (data.imagewebp_success ? 'Yes' : 'No') + '\n';
                        message += 'File Created: ' + (data.file_created ? 'Yes' : 'No') + '\n';
                        message += 'File Size: ' + (data.file_size || 'N/A') + '\n';
                        message += 'Valid WebP: ' + (data.valid_webp ? 'Yes' : 'No') + '\n\n';

                        if (data.error_details) {
                            message += 'Error Details:\n' + data.error_details + '\n\n';
                        }

                        if (data.php_errors && data.php_errors.length > 0) {
                            message += 'PHP Errors:\n';
                            data.php_errors.forEach(function(error) {
                                message += '- ' + error + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('WebP creation test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP creation test failed:', xhr.status, xhr.statusText);
                    alert('WebP creation test request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Reset specific images based on filenames
        resetSpecificImages: function() {
            console.log('🔧 Resetting specific images...');

            // The 3 problematic images from the log
            const imageFilenames = [
                '133913847558663271-scaled.jpg',
                '133921112396416462-scaled.jpg',
                'login-logo.jpg'
            ];

            const confirmMessage = 'Reset conversion data for these 3 images?\n\n' +
                                 imageFilenames.join('\n') +
                                 '\n\nThis will allow them to be re-converted with proper settings.';

            if (!confirm(confirmMessage)) {
                return;
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_reset_specific_images',
                    nonce: redcoWebP.nonces.stats,
                    filenames: imageFilenames
                },
                success: function(response) {
                    console.log('✅ Specific reset response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Specific Images Reset Results:\n\n';
                        message += 'Images Found: ' + data.images_found + '\n';
                        message += 'Records Cleared: ' + data.records_cleared + '\n';
                        message += 'WebP Files Deleted: ' + data.files_deleted + '\n\n';

                        if (data.processed_images && data.processed_images.length > 0) {
                            message += 'Processed Images:\n';
                            data.processed_images.forEach(function(img) {
                                message += '- ' + img + '\n';
                            });
                        }

                        message += '\nThese images can now be re-converted.';

                        alert(message);

                        // Refresh the page to update statistics
                        location.reload();
                    } else {
                        alert('Specific reset failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Specific reset failed:', xhr.status, xhr.statusText);
                    alert('Specific reset request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test image detection query
        testImageDetection: function() {
            console.log('🔧 Testing image detection query...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_image_detection'
                },
                success: function(response) {
                    console.log('✅ Image detection test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Image Detection Test Results:\n\n';
                        message += 'Total Images: ' + data.all_images_count + '\n';
                        message += 'Unconverted Images: ' + data.unconverted_images_count + '\n';
                        message += 'Existing Conversions: ' + data.existing_conversions_count + '\n\n';

                        if (data.all_images_count > 0) {
                            message += 'All Images:\n';
                            data.all_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        if (data.unconverted_images_count > 0) {
                            message += '\nUnconverted Images:\n';
                            data.unconverted_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Image detection test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image detection test failed:', xhr.status, xhr.statusText);
                    alert('Image detection test failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Check image status and conversion data
        checkImageStatus: function() {
            console.log('🔧 Checking image status...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ Image status response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = '📊 IMAGE STATUS REPORT\n\n';
                        message += '📁 Total Attachments: ' + data.total_attachments + '\n';
                        message += '🖼️ Total Images: ' + data.total_images + '\n';
                        message += '🆔 Image IDs: ' + data.image_ids.join(', ') + '\n\n';

                        message += '📋 MIME Types Found:\n';
                        data.all_attachment_types.forEach(function(type) {
                            message += '  • ' + type + '\n';
                        });

                        message += '\n🔍 Image MIME Types:\n';
                        data.mime_types.forEach(function(type, index) {
                            message += '  • ID ' + data.image_ids[index] + ': ' + type + '\n';
                        });

                        alert(message);
                    } else {
                        alert('❌ Image status check failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image status check failed:', xhr.status, xhr.statusText);
                    alert('❌ Image status check failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Close modal
        closeModal: function() {
            if (this.isConverting) {
                if (confirm('Are you sure you want to cancel the conversion?')) {
                    this.isConverting = false;
                    $('#webp-progress-modal').hide();
                }
            } else {
                $('#webp-progress-modal').hide();
            }
        },

        // Enhanced error handling with actionable feedback
        handleError: function(message) {
            this.isConverting = false;

            // Update modal title to indicate error
            $('#webp-progress-modal-title').text('WebP Conversion Error');

            // Create enhanced error message with actionable feedback
            let errorHtml = '<div style="color: #d63638; padding: 15px; background: #fef7f7; border: 1px solid #f5c6cb; border-radius: 4px;">';
            errorHtml += '<strong>❌ Conversion Failed</strong><br><br>';
            errorHtml += '<div style="margin-bottom: 10px;">' + message + '</div>';

            // Add actionable suggestions based on error type
            if (message.includes('Security verification failed') || message.includes('nonce')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Please refresh the page and try again.';
                errorHtml += '</div>';
            } else if (message.includes('permissions') || message.includes('Access denied')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Please contact your administrator to check user permissions.';
                errorHtml += '</div>';
            } else if (message.includes('module is not enabled') || message.includes('MODULE_DISABLED')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Go to the Modules page and enable the WebP conversion module.';
                errorHtml += '</div>';
            } else if (message.includes('server does not support') || message.includes('WebP')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Contact your hosting provider to enable GD library with WebP support.';
                errorHtml += '</div>';
            } else if (message.includes('Network error') || message.includes('Connection failed')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Check your internet connection and try again.';
                errorHtml += '</div>';
            } else {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Suggestions:</strong><br>';
                errorHtml += '• Refresh the page and try again<br>';
                errorHtml += '• Check if the WebP module is enabled<br>';
                errorHtml += '• Verify your server supports WebP conversion<br>';
                errorHtml += '• Contact support if the problem persists';
                errorHtml += '</div>';
            }

            errorHtml += '</div>';

            $('.current-file').html(errorHtml);

            // Add error to log
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const errorEntry = $('<div class="log-entry log-error">')
                    .html('<span class="dashicons dashicons-warning"></span> <strong>Conversion stopped due to error:</strong><br>' +
                          '<small>' + message + '</small>');
                logContainer.append(errorEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Update buttons
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Log error for debugging
            console.error('WebP Conversion Error:', message);
        },

        // Update WebP settings summary
        updateWebPSummary: function() {
            var enabledCount = 0;
            var estimatedSavings = 0;

            // Count enabled optimizations
            $('.webp-checkbox:checked').each(function() {
                enabledCount++;

                // Calculate estimated savings based on option
                var optionName = $(this).attr('name');
                if (optionName && optionName.includes('auto_convert_uploads')) {
                    estimatedSavings += 30; // 30% average savings for new uploads
                } else if (optionName && optionName.includes('replace_in_content')) {
                    estimatedSavings += 25; // 25% average savings for content replacement
                } else if (optionName && optionName.includes('backup_originals')) {
                    // Backup doesn't add savings, but adds safety
                }
            });

            // Cap savings at reasonable maximum
            estimatedSavings = Math.min(estimatedSavings, 35);

            // Update summary display
            $('.webp-summary .enabled-count').text(enabledCount + ' optimizations enabled');
            $('.webp-summary .estimated-savings').text('Estimated savings: ' + estimatedSavings + '%');

            // Update summary styling based on enabled count
            var summaryStats = $('.webp-summary .summary-stats');
            summaryStats.removeClass('low-optimization medium-optimization high-optimization');

            if (enabledCount === 0) {
                summaryStats.addClass('low-optimization');
            } else if (enabledCount <= 2) {
                summaryStats.addClass('medium-optimization');
            } else {
                summaryStats.addClass('high-optimization');
            }
        },

        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';

            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        },

        // Enhanced WebP Test Function
        testEnhancedWebP: function() {
            console.log('🚀 Starting Enhanced WebP Test...');

            const $button = $('#test-enhanced-webp');
            const originalText = $button.text();
            $button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_enhanced_test',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    $button.prop('disabled', false).text(originalText);
                    console.log('Enhanced WebP Test Results:', response);

                    if (response.success) {
                        const data = response.data;

                        console.log('=== ENHANCED SERVER CAPABILITIES ===');
                        console.log('WebP Support:', data.capabilities.webp_support ? '✅ YES' : '❌ NO');
                        console.log('Memory Available:', data.capabilities.memory_available ? '✅ YES' : '❌ NO');
                        console.log('Write Permissions:', data.capabilities.write_permissions ? '✅ YES' : '❌ NO');
                        console.log('GD Info:', data.capabilities.gd_info);

                        if (data.test_conversion) {
                            console.log('');
                            console.log('=== ENHANCED TEST CONVERSION ===');
                            if (data.test_conversion.success) {
                                console.log('✅ Enhanced test conversion SUCCESSFUL!');
                                console.log('Image ID:', data.test_conversion.image_id);
                                console.log('Original File:', data.test_conversion.original_file);
                                console.log('WebP File:', data.test_conversion.webp_file);
                                console.log('Original Size:', data.test_conversion.original_size, 'bytes');
                                console.log('WebP Size:', data.test_conversion.webp_size, 'bytes');
                                console.log('Savings:', data.test_conversion.original_size - data.test_conversion.webp_size, 'bytes');

                                alert('✅ Enhanced WebP Test PASSED!\n\n' +
                                      'Server supports WebP conversion with enhanced features.\n' +
                                      'Test conversion successful!\n\n' +
                                      'Check console for detailed results.');
                            } else {
                                console.log('❌ Enhanced test conversion failed:', data.test_conversion.error);
                                alert('❌ Enhanced WebP Test FAILED!\n\n' +
                                      'Test conversion failed: ' + data.test_conversion.error + '\n\n' +
                                      'Check console for detailed results.');
                            }
                        }

                        if (data.logs && data.logs.length > 0) {
                            console.log('');
                            console.log('=== ENHANCED LOGS ===');
                            data.logs.forEach(log => console.log(log));
                        }

                    } else {
                        console.log('❌ Enhanced WebP test failed:', response.data);
                        alert('❌ Enhanced WebP Test FAILED!\n\n' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    $button.prop('disabled', false).text(originalText);
                    console.log('❌ Enhanced WebP Test AJAX Error:', error);
                    alert('❌ Enhanced WebP Test Error!\n\nAJAX request failed: ' + error);
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Multiple detection methods for better reliability
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0 ||
                          $('#test-webp-modal').length > 0;

        if (isWebPPage) {
            WebPAdmin.init();
        }
    });

    // Make WebPAdmin globally available
    window.WebPAdmin = WebPAdmin;

    // Fallback initialization - try again after a short delay
    setTimeout(function() {
        if (typeof window.WebPAdmin !== 'undefined' && !window.WebPAdmin.initialized) {
            if ($('#test-webp-modal').length > 0) {
                WebPAdmin.init();
                WebPAdmin.initialized = true;
            }
        }
    }, 1000);

})(jQuery);

/**
 * Enhanced WebP Module Features
 * Dynamic button states, recent conversions, and modern UI
 */
(function($) {
    'use strict';

    window.RedcoWebPEnhanced = {
        initialized: false,
        currentOffset: 0,
        currentLimit: 10,
        currentSort: 'date',

        // Initialize enhanced features
        init: function() {
            if (this.initialized) return;

            console.log('🚀 Initializing Enhanced WebP Features');

            this.initDynamicButtonState();
            this.initRecentConversions();
            this.initToastNotifications();
            this.bindEvents();

            this.initialized = true;
        },

        // Initialize dynamic button state management
        initDynamicButtonState: function() {
            console.log('🔄 Initializing dynamic button state...');
            this.checkConvertibleImages();
        },

        // Check for convertible images and update button state
        checkConvertibleImages: function() {
            const self = this;

            console.log('🔧 WEBP BUTTON STATE: Checking convertible images...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    console.log('🔧 WEBP BUTTON STATE: AJAX response received:', response);

                    if (response.success) {
                        console.log('🔧 WEBP BUTTON STATE: Success - updating button with data:', response.data);
                        self.updateButtonState(response.data);
                    } else {
                        console.error('🔧 WEBP BUTTON STATE: Failed to get convertible images count:', response.data);
                        self.updateButtonState({
                            convertible_count: 0,
                            has_convertible_images: false,
                            message: 'Unable to check images'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('🔧 WEBP BUTTON STATE: AJAX error checking convertible images:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        error: error,
                        responseText: xhr.responseText
                    });
                    self.updateButtonState({
                        convertible_count: 0,
                        has_convertible_images: false,
                        message: 'Connection error'
                    });
                }
            });
        },

        // Update button state based on convertible images
        updateButtonState: function(data) {
            console.log('🔧 WEBP BUTTON STATE: updateButtonState called with data:', data);

            const $button = $('#bulk-convert-images');
            const $headerButton = $('#header-bulk-convert-images');
            const $buttonText = $('#convert-button-text');
            const $spinner = $('#convert-button-spinner');
            const $statusMessage = $('#conversion-status-message');
            const $statusText = $('#status-message-text');

            console.log('🔧 WEBP BUTTON STATE: Found elements:', {
                button: $button.length,
                headerButton: $headerButton.length,
                buttonText: $buttonText.length,
                spinner: $spinner.length,
                statusMessage: $statusMessage.length,
                statusText: $statusText.length
            });

            // Hide spinner
            $spinner.hide();



            if (data.has_convertible_images) {
                // Enable main button
                $button.prop('disabled', false)
                       .removeClass('button-secondary testing-mode')
                       .addClass('button-primary')
                       .css('background', '')
                       .css('border-color', '');

                // Enable header button
                $headerButton.prop('disabled', false);

                $buttonText.text(`Convert ${data.convertible_count} Images`);

                // Show status message
                $statusMessage.show();
                $statusText.text(data.message);
                $statusMessage.css('border-left-color', '#4CAF50');
                $statusMessage.find('.dashicons').removeClass('dashicons-info').addClass('dashicons-yes-alt');

            } else {
                // Disable main button
                $button.prop('disabled', true)
                       .removeClass('button-primary testing-mode')
                       .addClass('button-secondary')
                       .css('background', '')
                       .css('border-color', '');

                // Disable header button
                $headerButton.prop('disabled', true);

                $buttonText.text('No Images to Convert');

                // Show status message
                $statusMessage.show();
                $statusText.text(data.message || 'All images have been converted to WebP format');
                $statusMessage.css('border-left-color', '#666');
                $statusMessage.find('.dashicons').removeClass('dashicons-yes-alt').addClass('dashicons-info');
            }

            console.log(`✅ Button state updated: ${data.convertible_count} convertible images`);
        },

        // Initialize recent conversions functionality
        initRecentConversions: function() {
            console.log('📋 Initializing recent conversions...');
            this.loadRecentConversions();
        },

        // Load recent conversions via AJAX
        loadRecentConversions: function(append = false) {
            const self = this;

            if (!append) {
                $('#conversions-loading').show();
                $('#recent-conversions-list').hide();
                $('#conversions-empty').hide();
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_recent_conversions',
                    limit: this.currentLimit,
                    offset: append ? this.currentOffset : 0,
                    sort_by: this.currentSort,
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    $('#conversions-loading').hide();

                    if (response.success) {
                        self.renderConversions(response.data, append);
                    } else {
                        console.error('Failed to load recent conversions:', response.data);
                        $('#conversions-empty').show();
                    }
                },
                error: function(xhr, status, error) {
                    $('#conversions-loading').hide();
                    $('#conversions-empty').show();
                    console.error('AJAX error loading conversions:', error);
                }
            });
        },

        // Render conversions list
        renderConversions: function(data, append = false) {
            const $list = $('#recent-conversions-list');

            if (!append) {
                $list.empty();
                this.currentOffset = 0;
            }

            if (data.conversions.length === 0) {
                if (!append) {
                    $('#conversions-empty').show();
                }
                return;
            }

            $list.show();
            $('#conversions-empty').hide();

            // Render each conversion
            data.conversions.forEach(conversion => {
                const conversionHtml = this.renderConversionItem(conversion);
                $list.append(conversionHtml);
            });

            // Update pagination
            this.currentOffset += data.conversions.length;

            if (data.has_more) {
                $('#conversions-load-more').show();
            } else {
                $('#conversions-load-more').hide();
            }

            // Update pagination info
            $('#conversions-pagination-info').show();
            $('#pagination-text').text(`Showing ${this.currentOffset} of ${data.total_count} conversions`);

            console.log(`✅ Rendered ${data.conversions.length} conversions`);
        },

        // Render individual conversion item
        renderConversionItem: function(conversion) {
            const savingsClass = conversion.savings_percentage >= 20 ? 'high-savings' :
                                conversion.savings_percentage >= 10 ? 'medium-savings' : 'low-savings';

            return `
                <div class="conversion-item" data-id="${conversion.id}">
                    <div class="conversion-header">
                        <div class="conversion-thumbnail">
                            ${conversion.thumbnail_url ?
                                `<img src="${conversion.thumbnail_url}" alt="${conversion.title}" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">` :
                                `<span class="dashicons dashicons-format-image" style="font-size: 24px; color: #666;"></span>`
                            }
                        </div>
                        <div class="conversion-info">
                            <div class="conversion-title">${conversion.title}</div>
                            <div class="conversion-filename">${conversion.filename}</div>
                        </div>
                        <div class="conversion-actions">
                            <button type="button" class="button button-small view-details" data-id="${conversion.id}">
                                <span class="dashicons dashicons-visibility"></span>
                            </button>
                        </div>
                    </div>
                    <div class="conversion-stats">
                        <div class="stat-row">
                            <span class="stat-label">Original:</span>
                            <span class="stat-value">${conversion.formatted_original_size}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">WebP:</span>
                            <span class="stat-value">${conversion.formatted_webp_size}</span>
                        </div>
                        <div class="stat-row savings ${savingsClass}">
                            <span class="stat-label">Savings:</span>
                            <span class="stat-value">${conversion.savings_percentage}% (${conversion.formatted_savings})</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">Date:</span>
                            <span class="stat-value">${conversion.formatted_date}</span>
                        </div>
                    </div>
                </div>
            `;
        },

        // Initialize toast notifications
        initToastNotifications: function() {
            // Replace auto-save notices with toast notifications
            this.replaceAutoSaveNotices();
            this.interceptAutoSaveIndicators();
            this.overrideGlobalAutoSave();
        },

        // Replace auto-save notices with toast notifications
        replaceAutoSaveNotices: function() {
            // Use MutationObserver instead of deprecated DOMNodeInserted
            if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1) { // Element node
                                const $node = $(node);

                                // Check for WordPress admin notices
                                if ($node.hasClass('notice') || $node.find('.notice').length > 0) {
                                    const $notice = $node.hasClass('notice') ? $node : $node.find('.notice');
                                    const noticeText = $notice.text().trim();

                                    // Check if this is a Redco auto-save notice
                                    if (noticeText.includes('Auto-saved') || noticeText.includes('Settings saved') ||
                                        $notice.find('.redco-auto-save').length > 0) {

                                        // Remove the notice
                                        $notice.remove();

                                        // Show toast instead
                                        if (typeof RedcoToast !== 'undefined') {
                                            if (noticeText.includes('error') || noticeText.includes('failed')) {
                                                RedcoToast.error('Settings save failed', {
                                                    title: 'WebP Save Error',
                                                    duration: 6000,
                                                    className: 'webp-module'
                                                });
                                            } else {
                                                RedcoToast.success('Settings saved successfully', {
                                                    title: 'WebP Auto-saved',
                                                    duration: 3000,
                                                    className: 'webp-module'
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    });
                });

                // Start observing
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
        },

        // Intercept auto-save indicators and replace with toasts
        interceptAutoSaveIndicators: function() {
            const self = this;

            // Override the global auto-save indicator function if it exists
            if (typeof window.showAutoSaveIndicator === 'function') {
                const originalShowAutoSaveIndicator = window.showAutoSaveIndicator;

                window.showAutoSaveIndicator = function($form, state) {
                    // Check if this is the WebP module form
                    const isWebPForm = $form.hasClass('redco-module-form') &&
                                      $form.data('module') === 'smart-webp-conversion';

                    if (isWebPForm && typeof RedcoToast !== 'undefined') {
                        // Use toast instead of indicator
                        switch (state) {
                            case 'saving':
                                RedcoToast.info('Saving settings...', {
                                    title: 'Auto-save',
                                    duration: 2000,
                                    persistent: true
                                });
                                break;
                            case 'saved':
                                RedcoToast.success('Settings saved successfully', {
                                    title: 'Auto-saved',
                                    duration: 3000
                                });
                                break;
                            case 'error':
                                RedcoToast.error('Failed to save settings', {
                                    title: 'Save Error',
                                    duration: 6000
                                });
                                break;
                        }
                        return; // Don't show the original indicator
                    }

                    // For other modules, use the original function
                    originalShowAutoSaveIndicator.call(this, $form, state);
                };
            }

            // Also intercept direct auto-save indicator creation
            const originalAppend = $.fn.append;
            $.fn.append = function() {
                const args = Array.prototype.slice.call(arguments);

                // Check if we're appending an auto-save indicator to WebP form
                if (this.hasClass('redco-module-form') && this.data('module') === 'smart-webp-conversion') {
                    for (let i = 0; i < args.length; i++) {
                        const $arg = $(args[i]);
                        if ($arg.hasClass('auto-save-indicator')) {
                            // Don't append the indicator, show toast instead
                            const state = $arg.hasClass('saving') ? 'saving' :
                                         $arg.hasClass('saved') ? 'saved' :
                                         $arg.hasClass('error') ? 'error' : 'info';

                            if (typeof RedcoToast !== 'undefined') {
                                switch (state) {
                                    case 'saving':
                                        RedcoToast.info('Saving settings...', {
                                            title: 'Auto-save',
                                            duration: 2000
                                        });
                                        break;
                                    case 'saved':
                                        RedcoToast.success('Settings saved successfully', {
                                            title: 'Auto-saved',
                                            duration: 3000
                                        });
                                        break;
                                    case 'error':
                                        RedcoToast.error('Failed to save settings', {
                                            title: 'Save Error',
                                            duration: 6000
                                        });
                                        break;
                                }
                            }
                            return this; // Don't append the indicator
                        }
                    }
                }

                // For everything else, use the original append
                return originalAppend.apply(this, args);
            };
        },

        // Override global auto-save functions specifically for WebP module
        overrideGlobalAutoSave: function() {
            const self = this;

            // Wait for the global admin scripts to load
            $(document).ready(function() {
                // Override the performAutoSave function if it exists in global scope
                if (typeof window.performAutoSave === 'function') {
                    const originalPerformAutoSave = window.performAutoSave;

                    window.performAutoSave = function($form, module) {
                        // Check if this is the WebP module
                        if (module === 'smart-webp-conversion' && typeof RedcoToast !== 'undefined') {
                            // Show saving toast
                            const savingToast = RedcoToast.info('Saving settings...', {
                                title: 'WebP Auto-save',
                                duration: 0, // Don't auto-dismiss
                                persistent: true,
                                closable: false,
                                className: 'webp-module'
                            });

                            // Call the original function but intercept its success/error handling
                            const originalAjax = $.ajax;
                            $.ajax = function(options) {
                                if (options.data && options.data.module === 'smart-webp-conversion') {
                                    // Override success and error callbacks
                                    const originalSuccess = options.success;
                                    const originalError = options.error;

                                    options.success = function(response) {
                                        // Dismiss saving toast
                                        if (savingToast && typeof RedcoToast.dismiss === 'function') {
                                            RedcoToast.dismiss(savingToast);
                                        }

                                        // Show success toast
                                        if (response.success) {
                                            RedcoToast.success('Settings saved successfully', {
                                                title: 'WebP Auto-saved',
                                                duration: 3000,
                                                className: 'webp-module'
                                            });
                                        } else {
                                            RedcoToast.error('Failed to save settings', {
                                                title: 'WebP Save Error',
                                                duration: 6000,
                                                className: 'webp-module'
                                            });
                                        }

                                        // Call original success handler
                                        if (originalSuccess) {
                                            originalSuccess.call(this, response);
                                        }
                                    };

                                    options.error = function(xhr, status, error) {
                                        // Dismiss saving toast
                                        if (savingToast && typeof RedcoToast.dismiss === 'function') {
                                            RedcoToast.dismiss(savingToast);
                                        }

                                        // Show error toast
                                        RedcoToast.error('Connection error while saving', {
                                            title: 'WebP Save Error',
                                            duration: 6000,
                                            className: 'webp-module'
                                        });

                                        // Call original error handler
                                        if (originalError) {
                                            originalError.call(this, xhr, status, error);
                                        }
                                    };

                                    // Restore original $.ajax after this call
                                    const result = originalAjax.call(this, options);
                                    $.ajax = originalAjax;
                                    return result;
                                }

                                // For other modules, use original ajax
                                return originalAjax.call(this, options);
                            };

                            // Call original function
                            const result = originalPerformAutoSave.call(this, $form, module);

                            // Restore original $.ajax
                            $.ajax = originalAjax;

                            return result;
                        }

                        // For other modules, use original function
                        return originalPerformAutoSave.call(this, $form, module);
                    };
                }

                // Also override showAutoSaveIndicator and hideAutoSaveIndicator globally
                if (typeof window.showAutoSaveIndicator === 'function') {
                    const originalShowAutoSaveIndicator = window.showAutoSaveIndicator;

                    window.showAutoSaveIndicator = function($form, state) {
                        // Check if this is the WebP module form
                        if ($form.data('module') === 'smart-webp-conversion') {
                            // Don't show the indicator, we're using toasts
                            return;
                        }

                        // For other modules, use the original function
                        return originalShowAutoSaveIndicator.call(this, $form, state);
                    };
                }

                if (typeof window.hideAutoSaveIndicator === 'function') {
                    const originalHideAutoSaveIndicator = window.hideAutoSaveIndicator;

                    window.hideAutoSaveIndicator = function($form) {
                        // Check if this is the WebP module form
                        if ($form.data('module') === 'smart-webp-conversion') {
                            // Don't hide anything, we're using toasts
                            return;
                        }

                        // For other modules, use the original function
                        return originalHideAutoSaveIndicator.call(this, $form);
                    };
                }
            });
        },

        // Bind events for enhanced features
        bindEvents: function() {
            const self = this;

            // Conversions sorting
            $('#conversions-sort').on('change', function() {
                self.currentSort = $(this).val();
                self.currentOffset = 0;
                self.loadRecentConversions();
            });

            // Load more conversions
            $('#load-more-conversions').on('click', function() {
                self.loadRecentConversions(true);
            });

            // View conversion details
            $(document).on('click', '.view-details', function() {
                const conversionId = $(this).data('id');
                self.showConversionDetails(conversionId);
            });

            // Refresh convertible count after conversion
            $(document).on('webp-conversion-completed', function() {
                setTimeout(() => {
                    self.checkConvertibleImages();
                    self.loadRecentConversions();
                }, 1000);
            });

            // REMOVED: Auto-convert checkbox should NOT trigger button state checks
            // The auto-convert setting is purely for new uploads, not existing images

            // Refresh button state periodically
            setInterval(() => {
                if ($('#bulk-convert-images').is(':visible')) {
                    self.checkConvertibleImages();
                }
            }, 30000); // Every 30 seconds


        },

        // Show conversion details modal
        showConversionDetails: function(conversionId) {
            // Implementation for showing detailed conversion information
            console.log('Showing details for conversion:', conversionId);
            RedcoToast.info('Conversion details feature coming soon');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Check if we're on the WebP module page
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0;

        if (isWebPPage) {
            RedcoWebPEnhanced.init();
        }
    });

})(jQuery);

// Comprehensive WebP diagnostic function
window.testWebPDiagnostics = function() {
    console.log('🔧 Running Comprehensive WebP Diagnostics...');

    // Test 1: Check convertible images count
    jQuery.ajax({
        url: redcoWebP.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_webp_get_convertible_count',
            nonce: redcoWebP.nonces.bulk_convert
        },
        success: function(response) {
            console.log('📊 CONVERTIBLE COUNT:', response);

            // Test 2: Get WebP stats
            jQuery.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(statsResponse) {
                    console.log('📊 WEBP STATS:', statsResponse);

                    // Test 3: Debug image detection
                    jQuery.ajax({
                        url: redcoWebP.ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'redco_webp_debug_images',
                            nonce: redcoWebP.nonces.stats
                        },
                        success: function(debugResponse) {
                            console.log('📊 IMAGE DEBUG:', debugResponse);

                            // Compile comprehensive report
                            const report = {
                                convertible_count: response.success ? response.data.convertible_count : 'Error',
                                total_images: debugResponse.success ? debugResponse.data.total_images : 'Error',
                                converted_images: (statsResponse.success || statsResponse.data) ?
                                    (statsResponse.data || statsResponse).total_converted || 0 : 'Error',
                                mime_types: debugResponse.success ? debugResponse.data.mime_types : 'Error'
                            };

                            console.log('📋 COMPREHENSIVE REPORT:', report);

                            if (typeof RedcoToast !== 'undefined') {
                                const message = `Images: ${report.total_images} total, ${report.converted_images} converted, ${report.convertible_count} convertible`;
                                RedcoToast.info(message, {
                                    title: 'WebP Diagnostics Complete',
                                    duration: 8000
                                });
                            }

                            // Provide recommendations
                            if (report.total_images === 0) {
                                console.log('💡 RECOMMENDATION: Upload some JPEG/PNG images to Media Library');
                            } else if (report.convertible_count === 0 && report.converted_images > 0) {
                                console.log('💡 RECOMMENDATION: All images already converted to WebP');
                            } else if (report.convertible_count === 0) {
                                console.log('💡 RECOMMENDATION: Check if images are valid JPEG/PNG format');
                            }
                        },
                        error: function() {
                            console.log('📊 IMAGE DEBUG: Failed (endpoint may not exist)');
                        }
                    });
                },
                error: function() {
                    console.log('📊 WEBP STATS: Failed to get stats');
                }
            });
        },
        error: function(xhr, status, error) {
            console.error('📊 CONVERTIBLE COUNT: AJAX error', error);
        }
    });
};

// Debug function to test convertible images count
window.testWebPConvertibleCount = function() {
    console.log('🔧 Testing WebP Convertible Images Count...');

    jQuery.ajax({
        url: redcoWebP.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_webp_get_convertible_count',
            nonce: redcoWebP.nonces.bulk_convert
        },
        success: function(response) {
            console.log('🔧 Convertible Count Response:', response);
            if (response.success) {
                const data = response.data;
                console.log('🔧 Convertible Count:', data.convertible_count);
                console.log('🔧 Has Convertible Images:', data.has_convertible_images);
                console.log('🔧 Message:', data.message);

                if (typeof RedcoToast !== 'undefined') {
                    RedcoToast.info(`Found ${data.convertible_count} convertible images`, {
                        title: 'WebP Image Count',
                        duration: 5000
                    });
                }
            } else {
                console.error('🔧 Failed to get convertible count:', response.data);
                if (typeof RedcoToast !== 'undefined') {
                    RedcoToast.error('Failed to get convertible images count', {
                        title: 'Count Error',
                        duration: 5000
                    });
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('🔧 AJAX error getting convertible count:', error);
            if (typeof RedcoToast !== 'undefined') {
                RedcoToast.error('Network error while checking images', {
                    title: 'Connection Error',
                    duration: 5000
                });
            }
        }
    });
};

// Debug function to test auto-convert setting
window.testWebPAutoConvertSetting = function() {
    console.log('🔧 Testing WebP Auto-Convert Setting...');

    jQuery.ajax({
        url: redcoAjax.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_debug_get_option',
            option_name: 'redco_optimizer_smart_webp_conversion',
            nonce: redcoAjax.nonce
        },
        success: function(response) {
            if (response.success) {
                console.log('🔧 WebP Settings Debug:', response.data);
                if (response.data.option_value && response.data.option_value.auto_convert_uploads !== undefined) {
                    const value = response.data.option_value.auto_convert_uploads;
                    console.log('🔧 auto_convert_uploads value:', value);
                    console.log('🔧 auto_convert_uploads type:', typeof value);
                    console.log('🔧 Boolean evaluation:', !!value);

                    // Test the NEW robust logic used in the WebP module
                    let auto_convert_enabled = false;
                    if (typeof value === 'boolean') {
                        auto_convert_enabled = value;
                    } else if (typeof value === 'number') {
                        auto_convert_enabled = value === 1;
                    } else if (typeof value === 'string') {
                        auto_convert_enabled = ['1', 'true', 'on', 'yes'].includes(value.toLowerCase());
                    }

                    console.log('🔧 NEW Module logic result:', auto_convert_enabled);
                    console.log('🔧 Upload conversion will be:', auto_convert_enabled ? 'ENABLED' : 'DISABLED');

                    // Show user-friendly result
                    if (typeof RedcoToast !== 'undefined') {
                        const message = auto_convert_enabled ?
                            'Auto-convert is ENABLED - new uploads will be converted to WebP' :
                            'Auto-convert is DISABLED - new uploads will remain in original format';

                        const toastType = auto_convert_enabled ? 'info' : 'success';
                        RedcoToast[toastType](message, {
                            title: 'WebP Auto-Convert Status',
                            duration: 5000
                        });
                    }
                } else {
                    console.warn('🔧 auto_convert_uploads setting not found in database');
                    if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.warning('Auto-convert setting not found - defaults to DISABLED', {
                            title: 'WebP Setting Missing',
                            duration: 5000
                        });
                    }
                }
            } else {
                console.error('Failed to get WebP settings:', response.data);
                if (typeof RedcoToast !== 'undefined') {
                    RedcoToast.error('Failed to retrieve WebP settings', {
                        title: 'Debug Error',
                        duration: 5000
                    });
                }
            }
        },
        error: function() {
            console.error('AJAX error getting WebP settings');
            if (typeof RedcoToast !== 'undefined') {
                RedcoToast.error('Network error while testing settings', {
                    title: 'Connection Error',
                    duration: 5000
                });
            }
        }
    });
};
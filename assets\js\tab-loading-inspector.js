/**
 * Tab Loading Inspector for Redco Optimizer
 * 
 * This script systematically tests all navigation tabs to ensure:
 * 1. Tab switching is instantaneous
 * 2. Loading splash screens are properly hidden
 * 3. AJAX-loaded content displays correctly
 * 4. All module tabs function consistently
 * 5. No broken or empty tab states
 */

(function($) {
    'use strict';

    const TabLoadingInspector = {
        
        // Test configuration
        config: {
            maxLoadTime: 5000, // Maximum acceptable load time (5 seconds)
            checkInterval: 100, // How often to check loading status (100ms)
            testDelay: 1000, // Delay between tab tests (1 second)
            enableLogging: true
        },

        // Test results storage
        results: {
            passed: [],
            failed: [],
            warnings: []
        },

        // Available tabs to test
        tabs: [
            { key: 'dashboard', name: 'Dashboard', hasAjax: true },
            { key: 'diagnostic-autofix', name: 'Diagnostic & Auto-Fix', hasAjax: true },
            { key: 'page-cache', name: 'Page Cache', hasAjax: false },
            { key: 'lazy-load', name: 'Lazy Load Images', hasAjax: false },
            { key: 'css-js-minifier', name: 'CSS/JS Minifier', hasAjax: false },
            { key: 'database-cleanup', name: 'Database Cleanup', hasAjax: false },
            { key: 'heartbeat-control', name: 'Heartbeat Control', hasAjax: false },
            { key: 'wordpress-core-tweaks', name: 'WordPress Core Tweaks', hasAjax: false },
            { key: 'critical-resource-optimizer', name: 'Critical Resource Optimizer', hasAjax: false }
        ],

        /**
         * Initialize the inspector
         */
        init: function() {
            this.log('🔍 Tab Loading Inspector initialized');
            this.createInspectorUI();
            this.bindEvents();
        },

        /**
         * Create inspector UI
         */
        createInspectorUI: function() {
            const inspectorHtml = `
                <div id="redco-tab-inspector" style="
                    position: fixed;
                    top: 32px;
                    right: 20px;
                    width: 350px;
                    background: #fff;
                    border: 2px solid #4CAF50;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 99998;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    display: none;
                ">
                    <div style="
                        background: #4CAF50;
                        color: white;
                        padding: 12px 16px;
                        font-weight: 600;
                        border-radius: 6px 6px 0 0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <span>🔍 Tab Loading Inspector</span>
                        <button id="close-inspector" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 18px;
                            cursor: pointer;
                            padding: 0;
                            width: 24px;
                            height: 24px;
                        ">×</button>
                    </div>
                    <div style="padding: 16px;">
                        <div style="margin-bottom: 16px;">
                            <button id="run-all-tests" style="
                                background: #4CAF50;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-weight: 500;
                                width: 100%;
                            ">Run All Tab Tests</button>
                        </div>
                        <div id="test-progress" style="
                            background: #f5f5f5;
                            border-radius: 4px;
                            padding: 12px;
                            margin-bottom: 16px;
                            display: none;
                        ">
                            <div style="font-weight: 500; margin-bottom: 8px;">Testing Progress:</div>
                            <div id="current-test" style="color: #666; font-size: 14px;"></div>
                            <div style="
                                background: #e0e0e0;
                                height: 4px;
                                border-radius: 2px;
                                margin-top: 8px;
                                overflow: hidden;
                            ">
                                <div id="progress-bar" style="
                                    background: #4CAF50;
                                    height: 100%;
                                    width: 0%;
                                    transition: width 0.3s ease;
                                "></div>
                            </div>
                        </div>
                        <div id="test-results" style="
                            max-height: 300px;
                            overflow-y: auto;
                            font-size: 14px;
                        "></div>
                    </div>
                </div>
            `;

            $('body').append(inspectorHtml);
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            // Show inspector with Ctrl+Shift+I
            $(document).on('keydown', (e) => {
                if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                    e.preventDefault();
                    this.toggleInspector();
                }
            });

            // Close inspector
            $(document).on('click', '#close-inspector', () => {
                this.hideInspector();
            });

            // Run all tests
            $(document).on('click', '#run-all-tests', () => {
                this.runAllTests();
            });
        },

        /**
         * Toggle inspector visibility
         */
        toggleInspector: function() {
            const $inspector = $('#redco-tab-inspector');
            if ($inspector.is(':visible')) {
                this.hideInspector();
            } else {
                this.showInspector();
            }
        },

        /**
         * Show inspector
         */
        showInspector: function() {
            $('#redco-tab-inspector').fadeIn(300);
            this.log('🔍 Tab Inspector UI shown (Ctrl+Shift+I to toggle)');
        },

        /**
         * Hide inspector
         */
        hideInspector: function() {
            $('#redco-tab-inspector').fadeOut(300);
        },

        /**
         * Run all tab tests
         */
        runAllTests: function() {
            this.log('🚀 Starting comprehensive tab loading tests...');
            this.results = { passed: [], failed: [], warnings: [] };
            
            $('#test-progress').show();
            $('#test-results').html('<div style="color: #666;">Initializing tests...</div>');
            
            this.runTestSequence(0);
        },

        /**
         * Run test sequence for all tabs
         */
        runTestSequence: function(tabIndex) {
            if (tabIndex >= this.tabs.length) {
                this.showFinalResults();
                return;
            }

            const tab = this.tabs[tabIndex];
            const progress = ((tabIndex / this.tabs.length) * 100).toFixed(0);
            
            $('#current-test').text(`Testing: ${tab.name}`);
            $('#progress-bar').css('width', progress + '%');
            
            this.testTab(tab).then(() => {
                setTimeout(() => {
                    this.runTestSequence(tabIndex + 1);
                }, this.config.testDelay);
            });
        },

        /**
         * Test individual tab
         */
        testTab: function(tab) {
            return new Promise((resolve) => {
                this.log(`🔍 Testing tab: ${tab.name} (${tab.key})`);
                
                const startTime = Date.now();
                const testResult = {
                    tab: tab,
                    startTime: startTime,
                    loadTime: null,
                    contentLoaded: false,
                    ajaxContentLoaded: false,
                    errors: []
                };

                // Navigate to tab
                const tabUrl = this.buildTabUrl(tab.key);
                window.location.href = tabUrl;

                // Wait for navigation and test content loading
                setTimeout(() => {
                    this.checkTabContent(testResult, resolve);
                }, 500);
            });
        },

        /**
         * Check tab content loading
         */
        checkTabContent: function(testResult, resolve) {
            const checkStartTime = Date.now();
            const maxCheckTime = this.config.maxLoadTime;
            
            const checkInterval = setInterval(() => {
                const elapsed = Date.now() - checkStartTime;
                
                if (elapsed > maxCheckTime) {
                    clearInterval(checkInterval);
                    testResult.errors.push('Timeout: Content did not load within ' + (maxCheckTime/1000) + ' seconds');
                    this.recordTestResult(testResult);
                    resolve();
                    return;
                }

                // Check if content is loaded
                const contentChecks = this.performContentChecks(testResult.tab);
                
                if (contentChecks.basicContent && (!testResult.tab.hasAjax || contentChecks.ajaxContent)) {
                    clearInterval(checkInterval);
                    testResult.loadTime = Date.now() - testResult.startTime;
                    testResult.contentLoaded = contentChecks.basicContent;
                    testResult.ajaxContentLoaded = contentChecks.ajaxContent;
                    
                    this.recordTestResult(testResult);
                    resolve();
                }
            }, this.config.checkInterval);
        },

        /**
         * Perform content checks for a tab
         */
        performContentChecks: function(tab) {
            const checks = {
                basicContent: false,
                ajaxContent: false,
                loadingScreenHidden: false
            };

            // Check basic content
            const basicSelectors = [
                '.redco-content',
                '.redco-module-content',
                '.redco-module-settings',
                '.module-settings-content'
            ];

            for (let selector of basicSelectors) {
                if ($(selector).length > 0 && $(selector).is(':visible')) {
                    checks.basicContent = true;
                    break;
                }
            }

            // Check AJAX content for specific tabs
            if (tab.hasAjax) {
                if (tab.key === 'dashboard') {
                    checks.ajaxContent = this.checkDashboardAjaxContent();
                } else if (tab.key === 'diagnostic-autofix') {
                    checks.ajaxContent = this.checkDiagnosticAjaxContent();
                }
            } else {
                checks.ajaxContent = true; // No AJAX content expected
            }

            // Check if loading screen is hidden
            checks.loadingScreenHidden = !$('#redco-universal-loading-overlay').is(':visible');

            return checks;
        },

        /**
         * Check dashboard AJAX content
         */
        checkDashboardAjaxContent: function() {
            const statsCards = $('.redco-stats-cards .stat-card');
            const healthDashboard = $('.redco-performance-health-dashboard');
            
            let hasRealData = false;
            
            // Check stats cards for real data
            statsCards.each(function() {
                const statNumber = $(this).find('.stat-number, .score-number, .metric-number').text().trim();
                if (statNumber && statNumber !== '-' && statNumber !== '...' && statNumber !== 'Loading...') {
                    hasRealData = true;
                    return false;
                }
            });
            
            // Check health dashboard for real data
            if (!hasRealData && healthDashboard.length > 0) {
                const healthScores = healthDashboard.find('.score-number, .metric-value').filter(':visible');
                healthScores.each(function() {
                    const scoreText = $(this).text().trim();
                    if (scoreText && scoreText !== '-' && scoreText !== '...' && scoreText !== 'Loading...') {
                        hasRealData = true;
                        return false;
                    }
                });
            }
            
            return hasRealData;
        },

        /**
         * Check diagnostic AJAX content
         */
        checkDiagnosticAjaxContent: function() {
            const diagnosticCards = $('.diagnostic-overview-grid .overview-stat');
            const issuesList = $('.issues-list');
            
            return diagnosticCards.length > 0 || issuesList.length > 0;
        },

        /**
         * Record test result
         */
        recordTestResult: function(testResult) {
            const tab = testResult.tab;
            const loadTime = testResult.loadTime;
            const hasErrors = testResult.errors.length > 0;
            
            if (hasErrors) {
                this.results.failed.push(testResult);
                this.log(`❌ FAILED: ${tab.name} - ${testResult.errors.join(', ')}`);
            } else if (loadTime > 3000) {
                this.results.warnings.push(testResult);
                this.log(`⚠️ WARNING: ${tab.name} - Slow loading (${loadTime}ms)`);
            } else {
                this.results.passed.push(testResult);
                this.log(`✅ PASSED: ${tab.name} - Loaded in ${loadTime}ms`);
            }
            
            this.updateResultsDisplay();
        },

        /**
         * Update results display
         */
        updateResultsDisplay: function() {
            const resultsHtml = `
                <div style="margin-bottom: 12px;">
                    <div style="color: #4CAF50; font-weight: 500;">✅ Passed: ${this.results.passed.length}</div>
                    <div style="color: #FF9800; font-weight: 500;">⚠️ Warnings: ${this.results.warnings.length}</div>
                    <div style="color: #F44336; font-weight: 500;">❌ Failed: ${this.results.failed.length}</div>
                </div>
            `;
            
            $('#test-results').html(resultsHtml);
        },

        /**
         * Show final results
         */
        showFinalResults: function() {
            $('#test-progress').hide();
            $('#current-test').text('Tests completed!');
            $('#progress-bar').css('width', '100%');
            
            const totalTests = this.results.passed.length + this.results.warnings.length + this.results.failed.length;
            const successRate = ((this.results.passed.length / totalTests) * 100).toFixed(1);
            
            let resultsHtml = `
                <div style="margin-bottom: 16px; padding: 12px; background: #f0f8ff; border-radius: 4px;">
                    <div style="font-weight: 600; margin-bottom: 8px;">📊 Test Summary</div>
                    <div>Total Tests: ${totalTests}</div>
                    <div>Success Rate: ${successRate}%</div>
                </div>
                
                <div style="margin-bottom: 12px;">
                    <div style="color: #4CAF50; font-weight: 500;">✅ Passed: ${this.results.passed.length}</div>
                    <div style="color: #FF9800; font-weight: 500;">⚠️ Warnings: ${this.results.warnings.length}</div>
                    <div style="color: #F44336; font-weight: 500;">❌ Failed: ${this.results.failed.length}</div>
                </div>
            `;
            
            // Add detailed results
            if (this.results.failed.length > 0) {
                resultsHtml += '<div style="margin-top: 16px;"><strong>Failed Tests:</strong></div>';
                this.results.failed.forEach(result => {
                    resultsHtml += `<div style="color: #F44336; font-size: 12px; margin: 4px 0;">❌ ${result.tab.name}: ${result.errors.join(', ')}</div>`;
                });
            }
            
            if (this.results.warnings.length > 0) {
                resultsHtml += '<div style="margin-top: 16px;"><strong>Warnings:</strong></div>';
                this.results.warnings.forEach(result => {
                    resultsHtml += `<div style="color: #FF9800; font-size: 12px; margin: 4px 0;">⚠️ ${result.tab.name}: Slow loading (${result.loadTime}ms)</div>`;
                });
            }
            
            $('#test-results').html(resultsHtml);
            
            this.log(`🏁 All tests completed! Success rate: ${successRate}%`);
        },

        /**
         * Build tab URL
         */
        buildTabUrl: function(tabKey) {
            const baseUrl = window.location.href.split('?')[0];
            return `${baseUrl}?page=redco-optimizer&tab=${tabKey}`;
        },

        /**
         * Log message
         */
        log: function(message) {
            if (this.config.enableLogging) {
                console.log(`[TabInspector] ${message}`);
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize on Redco Optimizer admin pages
        if (window.location.href.includes('page=redco-optimizer')) {
            TabLoadingInspector.init();
            
            // Make globally accessible for manual testing
            window.RedcoTabInspector = TabLoadingInspector;
            
            console.log('🔍 Tab Loading Inspector ready! Press Ctrl+Shift+I to open inspector UI');
        }
    });

})(jQuery);

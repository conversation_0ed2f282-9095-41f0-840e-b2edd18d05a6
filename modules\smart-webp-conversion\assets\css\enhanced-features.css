/**
 * Enhanced WebP Module Features CSS
 * Modern styling for dynamic button states, recent conversions, and UI improvements
 */

/* Dynamic Button States */
#conversion-button-container {
    position: relative;
}

#bulk-convert-images {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#bulk-convert-images:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#bulk-convert-images.button-primary {
    background: #4CAF50;
    border-color: #4CAF50;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

#bulk-convert-images.button-primary:hover:not(:disabled) {
    background: #45a049;
    border-color: #45a049;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
    transform: translateY(-1px);
}

/* 🧪 Testing Mode Styles */
#bulk-convert-images.testing-mode {
    background: #FF9800 !important;
    border-color: #FF9800 !important;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
    position: relative;
    animation: testingPulse 2s infinite;
}

#bulk-convert-images.testing-mode:hover:not(:disabled) {
    background: #F57C00 !important;
    border-color: #F57C00 !important;
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.4);
    transform: translateY(-1px);
}

#bulk-convert-images.testing-mode::before {
    content: "🧪";
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    animation: testingBounce 1s infinite;
}

@keyframes testingPulse {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 12px rgba(255, 152, 0, 0.5);
    }
}

@keyframes testingBounce {
    0%, 100% {
        transform: translateY(-50%) scale(1);
    }
    50% {
        transform: translateY(-50%) scale(1.1);
    }
}

#convert-button-spinner {
    display: inline-block;
    margin-left: 8px;
}

#conversion-status-message {
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

#conversion-status-message .dashicons {
    vertical-align: middle;
    margin-top: -2px;
}

/* Recent Conversions */
.sidebar-section-header {
    position: relative;
}

.section-actions {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

#conversions-sort {
    border-radius: 4px;
    border: 1px solid #ddd;
    background: white;
}

#recent-conversions-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #fafafa;
}

.conversion-item {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 12px;
    transition: all 0.2s ease;
    position: relative;
}

.conversion-item:last-child {
    border-bottom: none;
}

.conversion-item:hover {
    background: #f8f9fa;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.conversion-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.conversion-thumbnail {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.conversion-info {
    flex: 1;
    min-width: 0;
}

.conversion-title {
    font-weight: 600;
    font-size: 13px;
    color: #1a1a1a;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.conversion-filename {
    font-size: 11px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversion-actions {
    flex-shrink: 0;
}

.view-details {
    padding: 4px 8px;
    min-height: auto;
    line-height: 1;
}

.view-details .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.conversion-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px 8px;
    font-size: 11px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.stat-value {
    color: #1a1a1a;
    font-weight: 600;
}

.stat-row.savings .stat-value {
    color: #4CAF50;
}

.stat-row.high-savings .stat-value {
    color: #2E7D32;
    font-weight: 700;
}

.stat-row.medium-savings .stat-value {
    color: #4CAF50;
}

.stat-row.low-savings .stat-value {
    color: #FF9800;
}

/* Loading and Empty States */
#conversions-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: #666;
    font-size: 14px;
}

#conversions-loading .spinner {
    margin-right: 8px;
}

#conversions-empty {
    text-align: center;
    padding: 30px 20px;
    color: #999;
}

#conversions-empty .dashicons {
    display: block;
    margin: 0 auto 12px;
    opacity: 0.5;
}

#conversions-empty p {
    margin: 0 0 8px 0;
    font-size: 14px;
}

/* Load More Button */
#conversions-load-more {
    padding: 10px;
    text-align: center;
}

#load-more-conversions {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #666;
    transition: all 0.2s ease;
}

#load-more-conversions:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
}

#load-more-conversions .dashicons {
    margin-right: 4px;
}

/* Pagination Info */
#conversions-pagination-info {
    text-align: center;
    color: #999;
    font-size: 11px;
    padding: 8px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Responsive Design */
@media (max-width: 768px) {
    .conversion-header {
        flex-wrap: wrap;
        gap: 8px;
    }

    .conversion-actions {
        order: -1;
        margin-left: auto;
    }

    .conversion-stats {
        grid-template-columns: 1fr;
        gap: 2px;
    }

    .section-actions {
        position: static;
        transform: none;
        margin-top: 8px;
    }

    .sidebar-section-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* Animation for new items */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.conversion-item.new-item {
    animation: slideInFromTop 0.3s ease-out;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .conversion-item {
        border: 1px solid #666;
    }

    .conversion-item:hover {
        border-color: #000;
    }

    .stat-row.savings .stat-value {
        color: #000;
        font-weight: 700;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #bulk-convert-images,
    .conversion-item,
    #load-more-conversions {
        transition: none;
    }

    #bulk-convert-images.button-primary:hover:not(:disabled) {
        transform: none;
    }

    .conversion-item.new-item {
        animation: none;
    }
}

/* Focus styles for accessibility */
.conversion-item:focus-within {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

.view-details:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

#conversions-sort:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

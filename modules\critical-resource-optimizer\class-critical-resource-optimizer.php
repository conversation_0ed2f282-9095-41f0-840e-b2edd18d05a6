<?php
/**
 * Critical Resource Optimizer Module for Redco Optimizer
 *
 * Optimizes critical above-the-fold resources by inlining critical CSS,
 * deferring non-critical resources, and implementing resource hints for
 * maximum PageSpeed Insights performance.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Critical_Resource_Optimizer {

    /**
     * Critical CSS cache directory
     */
    private $critical_css_dir;

    /**
     * Optimized resources cache
     */
    private $optimized_cache = array();

    /**
     * Critical viewport height (pixels)
     */
    private $critical_viewport_height = 600;

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('critical-resource-optimizer')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Set up cache directory
        $upload_dir = wp_upload_dir();
        $this->critical_css_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/critical-css/';

        // Create cache directory if it doesn't exist
        if (!file_exists($this->critical_css_dir)) {
            wp_mkdir_p($this->critical_css_dir);
        }

        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        // Settings are loaded via redco_get_module_option() when needed
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Load generated optimization files first
        $this->load_optimization_files();

        // Only optimize for non-admin, frontend pages
        if (!is_admin()) {
            add_action('wp_head', array($this, 'inject_critical_css'), 1);
            add_action('wp_head', array($this, 'add_resource_hints'), 2);
            add_filter('style_loader_tag', array($this, 'defer_non_critical_css'), 10, 4);
            add_filter('script_loader_tag', array($this, 'optimize_javascript_loading'), 10, 3);
            add_action('wp_head', array($this, 'optimize_font_loading'), 3);

            // Performance measurement disabled by default for optimal production performance
            // Only enable if explicitly requested via setting
            if (redco_get_module_option('critical-resource-optimizer', 'measure_performance', false)) {
                add_action('wp_head', array($this, 'add_performance_measurement'), 0);
                add_action('wp_footer', array($this, 'output_performance_measurement'), 999);
            }
        }

        // AJAX handlers (optimize_critical_resources is handled by Progress_Tracker)
        add_action('wp_ajax_redco_generate_critical_css', array($this, 'ajax_generate_critical_css'));
        add_action('wp_ajax_redco_track_performance', array($this, 'ajax_track_performance'));
        add_action('wp_ajax_nopriv_redco_track_performance', array($this, 'ajax_track_performance'));

        // Clear cache hooks
        add_action('save_post', array($this, 'clear_critical_cache'));
        add_action('switch_theme', array($this, 'clear_critical_cache'));
    }

    /**
     * Load generated optimization files to apply optimizations
     */
    private function load_optimization_files() {
        // Load resource hints file
        $resource_hints_file = WP_CONTENT_DIR . '/redco-resource-hints.php';
        if (file_exists($resource_hints_file)) {
            include_once $resource_hints_file;
        }

        // Load JavaScript optimizations file
        $js_optimizations_file = WP_CONTENT_DIR . '/redco-js-optimizations.php';
        if (file_exists($js_optimizations_file)) {
            include_once $js_optimizations_file;
        }

        // Load font optimizations file
        $font_optimizations_file = WP_CONTENT_DIR . '/redco-font-optimizations.php';
        if (file_exists($font_optimizations_file)) {
            include_once $font_optimizations_file;
        }
    }

    /**
     * Inject critical CSS into head
     */
    public function inject_critical_css() {
        if (!redco_get_module_option('critical-resource-optimizer', 'critical_css', true)) {
            return;
        }

        $critical_css = $this->get_critical_css();

        if (!empty($critical_css)) {
            echo '<!-- Redco Optimizer Critical CSS -->' . "\n";
            echo '<style id="redco-critical-css">' . $critical_css . '</style>' . "\n";
            echo '<!-- End Redco Critical CSS -->' . "\n";

            // Add preload for full stylesheet
            $this->add_stylesheet_preload();

            // Only log in debug mode
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Redco: Injected ' . strlen($critical_css) . ' bytes of critical CSS');
            }
        }
    }

    /**
     * Add resource hints for performance
     */
    public function add_resource_hints() {
        if (!redco_get_module_option('critical-resource-optimizer', 'resource_hints', true)) {
            return;
        }

        echo '<!-- Redco Optimizer Resource Hints -->' . "\n";

        // Preconnect to common external domains
        $preconnect_domains = $this->get_preconnect_domains();
        foreach ($preconnect_domains as $domain) {
            echo '<link rel="preconnect" href="' . esc_url($domain) . '" crossorigin>' . "\n";
        }

        // DNS prefetch for external resources
        $dns_prefetch_domains = $this->get_dns_prefetch_domains();
        foreach ($dns_prefetch_domains as $domain) {
            echo '<link rel="dns-prefetch" href="' . esc_url($domain) . '">' . "\n";
        }

        echo '<!-- End Redco Resource Hints -->' . "\n";

        // Only log in debug mode
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $total_hints = count($preconnect_domains) + count($dns_prefetch_domains);
            error_log('Redco: Added ' . $total_hints . ' resource hints');
        }
    }

    /**
     * Defer non-critical CSS using media="print" technique
     */
    public function defer_non_critical_css($html, $handle, $href, $media) {
        if (!redco_get_module_option('critical-resource-optimizer', 'defer_non_critical', true)) {
            return $html;
        }

        // Skip critical stylesheets
        if ($this->is_critical_stylesheet($handle)) {
            return $html;
        }

        // Skip admin stylesheets
        if (strpos($handle, 'admin') !== false) {
            return $html;
        }

        // Defer non-critical CSS
        $deferred_html = str_replace("media='$media'", "media='print' onload=\"this.media='$media'\"", $html);
        $deferred_html = str_replace('media="' . $media . '"', 'media="print" onload="this.media=\'' . $media . '\'"', $deferred_html);

        // Add noscript fallback
        $deferred_html .= '<noscript>' . $html . '</noscript>';

        return $deferred_html;
    }

    /**
     * Optimize JavaScript loading with async/defer
     */
    public function optimize_javascript_loading($tag, $handle, $src) {
        if (!redco_get_module_option('critical-resource-optimizer', 'optimize_js', true)) {
            return $tag;
        }

        // Skip jQuery and critical scripts
        if ($this->is_critical_script($handle)) {
            return $tag;
        }

        // Skip admin scripts
        if (strpos($handle, 'admin') !== false) {
            return $tag;
        }

        // Add async or defer based on script type
        if ($this->should_defer_script($handle)) {
            $tag = str_replace(' src', ' defer src', $tag);
        } else {
            $tag = str_replace(' src', ' async src', $tag);
        }

        return $tag;
    }

    /**
     * Optimize font loading with font-display: swap
     */
    public function optimize_font_loading() {
        if (!redco_get_module_option('critical-resource-optimizer', 'optimize_fonts', true)) {
            return;
        }

        echo '<style>@font-face{font-display:swap;}</style>' . "\n";
    }

    /**
     * Get critical CSS for current page
     */
    public function get_critical_css() {
        // Use transient caching to reduce file system operations
        $page_id = $this->get_page_identifier();
        $cache_key = 'redco_critical_css_' . $page_id;

        // Try to get from transient cache first
        $cached_css = get_transient($cache_key);
        if ($cached_css !== false) {
            return $cached_css;
        }

        // Try specific page critical CSS file
        $cache_file = $this->critical_css_dir . 'critical-' . $page_id . '.css';
        if (file_exists($cache_file)) {
            $css_content = file_get_contents($cache_file);
            // Cache in transient for 1 hour
            set_transient($cache_key, $css_content, HOUR_IN_SECONDS);
            return $css_content;
        }

        // Try homepage critical CSS as fallback
        $homepage_cache_file = $this->critical_css_dir . 'critical-homepage.css';
        if (file_exists($homepage_cache_file)) {
            $css_content = file_get_contents($homepage_cache_file);
            // Cache in transient for 1 hour
            set_transient($cache_key, $css_content, HOUR_IN_SECONDS);
            return $css_content;
        }

        // Return empty string if no critical CSS available (don't generate on every page load)
        return '';
    }

    /**
     * Generate critical CSS for current page
     */
    private function generate_critical_css() {
        // Get all stylesheets
        $stylesheets = $this->get_page_stylesheets();
        $critical_css = '';

        foreach ($stylesheets as $stylesheet) {
            $css_content = $this->get_stylesheet_content($stylesheet);
            $critical_rules = $this->extract_critical_rules($css_content);
            $critical_css .= $critical_rules;
        }

        // Minify critical CSS
        $critical_css = $this->minify_css($critical_css);

        // Cache the result
        $page_id = $this->get_page_identifier();
        $cache_file = $this->critical_css_dir . 'critical-' . $page_id . '.css';
        file_put_contents($cache_file, $critical_css);

        return $critical_css;
    }

    /**
     * Extract critical CSS rules (above-the-fold)
     */
    private function extract_critical_rules($css_content) {
        // Basic critical CSS extraction
        // In production, this would use more sophisticated parsing
        $critical_selectors = array(
            'body', 'html', 'header', '.header', '#header',
            'nav', '.nav', '.navigation', '.menu',
            'h1', 'h2', 'h3', '.title', '.heading',
            '.hero', '.banner', '.featured',
            '.above-fold', '.critical'
        );

        $critical_css = '';
        $lines = explode("\n", $css_content);
        $in_critical_rule = false;
        $brace_count = 0;

        foreach ($lines as $line) {
            $line = trim($line);

            // Check if line contains critical selector
            foreach ($critical_selectors as $selector) {
                if (strpos($line, $selector) !== false && strpos($line, '{') !== false) {
                    $in_critical_rule = true;
                    break;
                }
            }

            if ($in_critical_rule) {
                $critical_css .= $line . "\n";

                // Count braces to know when rule ends
                $brace_count += substr_count($line, '{') - substr_count($line, '}');

                if ($brace_count <= 0) {
                    $in_critical_rule = false;
                    $brace_count = 0;
                }
            }
        }

        return $critical_css;
    }

    /**
     * Get unique page identifier for caching
     */
    private function get_page_identifier() {
        if (is_front_page()) {
            return 'homepage';
        } elseif (is_single()) {
            return 'post-' . get_the_ID();
        } elseif (is_page()) {
            return 'page-' . get_the_ID();
        } elseif (is_category()) {
            return 'category-' . get_queried_object_id();
        } elseif (is_tag()) {
            return 'tag-' . get_queried_object_id();
        } else {
            return 'archive-' . md5($_SERVER['REQUEST_URI']);
        }
    }

    /**
     * Get all stylesheets for current page
     */
    private function get_page_stylesheets() {
        global $wp_styles;

        $stylesheets = array();

        if (isset($wp_styles->queue)) {
            foreach ($wp_styles->queue as $handle) {
                if (isset($wp_styles->registered[$handle])) {
                    $stylesheets[] = $wp_styles->registered[$handle]->src;
                }
            }
        }

        return $stylesheets;
    }

    /**
     * Get stylesheet content from URL or file
     */
    private function get_stylesheet_content($src) {
        // Convert relative URLs to absolute
        if (strpos($src, 'http') !== 0) {
            $src = home_url($src);
        }

        // Try to get local file first for better performance
        $local_path = $this->url_to_local_path($src);
        if ($local_path && file_exists($local_path)) {
            return file_get_contents($local_path);
        }

        // Fallback to HTTP request
        $response = wp_remote_get($src, array('timeout' => 10));
        if (!is_wp_error($response)) {
            return wp_remote_retrieve_body($response);
        }

        return '';
    }

    /**
     * Convert URL to local file path
     */
    private function url_to_local_path($url) {
        $home_url = home_url();
        if (strpos($url, $home_url) === 0) {
            $relative_path = str_replace($home_url, '', $url);
            return ABSPATH . ltrim($relative_path, '/');
        }
        return false;
    }

    /**
     * Minify CSS content
     */
    private function minify_css($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);

        // Remove unnecessary whitespace
        $css = str_replace(array("\r\n", "\r", "\n", "\t", '  ', '    ', '    '), '', $css);
        $css = str_replace(array('; ', ' ;', ' {', '{ ', ' }', '} ', ': ', ' :'), array(';', ';', '{', '{', '}', '}', ':', ':'), $css);

        return trim($css);
    }

    /**
     * Add stylesheet preload links
     */
    private function add_stylesheet_preload() {
        global $wp_styles;

        if (isset($wp_styles->queue)) {
            foreach ($wp_styles->queue as $handle) {
                if (isset($wp_styles->registered[$handle]) && !$this->is_critical_stylesheet($handle)) {
                    $src = $wp_styles->registered[$handle]->src;
                    echo '<link rel="preload" href="' . esc_url($src) . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
                }
            }
        }
    }

    /**
     * Check if stylesheet is critical and should not be deferred
     */
    private function is_critical_stylesheet($handle) {
        $critical_handles = array(
            'wp-block-library',
            'wp-block-library-theme',
            'global-styles'
        );

        return in_array($handle, $critical_handles);
    }

    /**
     * Check if script is critical and should not be deferred
     */
    private function is_critical_script($handle) {
        $critical_handles = array(
            'jquery',
            'jquery-core',
            'jquery-migrate',
            'wp-polyfill'
        );

        return in_array($handle, $critical_handles);
    }

    /**
     * Determine if script should be deferred vs async
     */
    private function should_defer_script($handle) {
        // Scripts that depend on DOM should be deferred
        $defer_handles = array(
            'wp-dom-ready',
            'wp-hooks',
            'wp-i18n'
        );

        return in_array($handle, $defer_handles);
    }

    /**
     * Get domains for preconnect hints
     */
    private function get_preconnect_domains() {
        $domains = array();

        // Google Fonts
        if (redco_get_module_option('critical-resource-optimizer', 'preconnect_google_fonts', true)) {
            $domains[] = 'https://fonts.googleapis.com';
            $domains[] = 'https://fonts.gstatic.com';
        }

        // Google Analytics
        if (redco_get_module_option('critical-resource-optimizer', 'preconnect_analytics', true)) {
            $domains[] = 'https://www.google-analytics.com';
            $domains[] = 'https://www.googletagmanager.com';
        }

        return apply_filters('redco_preconnect_domains', $domains);
    }

    /**
     * Get domains for DNS prefetch
     */
    private function get_dns_prefetch_domains() {
        $domains = array();

        // Common CDNs
        $domains[] = '//cdnjs.cloudflare.com';
        $domains[] = '//ajax.googleapis.com';
        $domains[] = '//code.jquery.com';

        return apply_filters('redco_dns_prefetch_domains', $domains);
    }

    /**
     * Add performance measurement script to head
     */
    public function add_performance_measurement() {
        if (!redco_get_module_option('critical-resource-optimizer', 'measure_performance', true)) {
            return;
        }

        echo '<!-- Redco Performance Measurement -->' . "\n";
        echo '<script>
        window.redcoPerformance = {
            startTime: performance.now(),
            measurements: {},

            mark: function(name) {
                this.measurements[name] = performance.now() - this.startTime;
            },

            getResults: function() {
                return {
                    domContentLoaded: this.measurements.domContentLoaded || 0,
                    windowLoaded: this.measurements.windowLoaded || 0,
                    firstPaint: this.measurements.firstPaint || 0,
                    firstContentfulPaint: this.measurements.firstContentfulPaint || 0
                };
            }
        };

        // Measure key performance metrics
        document.addEventListener("DOMContentLoaded", function() {
            window.redcoPerformance.mark("domContentLoaded");
        });

        window.addEventListener("load", function() {
            window.redcoPerformance.mark("windowLoaded");

            // Get paint metrics if available
            if (window.performance && window.performance.getEntriesByType) {
                const paintEntries = window.performance.getEntriesByType("paint");
                paintEntries.forEach(function(entry) {
                    if (entry.name === "first-paint") {
                        window.redcoPerformance.measurements.firstPaint = entry.startTime;
                    } else if (entry.name === "first-contentful-paint") {
                        window.redcoPerformance.measurements.firstContentfulPaint = entry.startTime;
                    }
                });
            }
        });
        </script>' . "\n";
        echo '<!-- End Redco Performance Measurement -->' . "\n";
    }

    /**
     * Output performance measurement results
     */
    public function output_performance_measurement() {
        if (!redco_get_module_option('critical-resource-optimizer', 'measure_performance', true)) {
            return;
        }

        echo '<!-- Redco Performance Results -->' . "\n";
        echo '<script>
        // Send performance data to console for debugging
        window.addEventListener("load", function() {
            setTimeout(function() {
                const results = window.redcoPerformance.getResults();
                console.log("🚀 Redco Optimizer Performance Results:", results);

                // Send to server for tracking (optional)
                if (window.fetch && typeof redcoAjax !== "undefined") {
                    fetch(redcoAjax.ajaxurl, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                        },
                        body: "action=redco_track_performance&nonce=" + redcoAjax.nonce + "&data=" + encodeURIComponent(JSON.stringify(results))
                    }).catch(function(error) {
                        console.log("Redco: Performance tracking failed", error);
                    });
                }
            }, 1000);
        });
        </script>' . "\n";
        echo '<!-- End Redco Performance Results -->' . "\n";
    }



    /**
     * AJAX handler for generating critical CSS
     */
    public function ajax_generate_critical_css() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $url = sanitize_url($_POST['url'] ?? home_url());
        $critical_css = $this->generate_critical_css_for_url($url);

        wp_send_json_success(array(
            'critical_css' => $critical_css,
            'url' => $url
        ));
    }

    /**
     * AJAX handler for tracking performance metrics
     */
    public function ajax_track_performance() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $performance_data = json_decode(stripslashes($_POST['data']), true);

        if ($performance_data) {
            // Store performance data in transient for dashboard display
            $stored_data = get_transient('redco_performance_metrics') ?: array();
            $stored_data[] = array(
                'timestamp' => time(),
                'url' => $_SERVER['HTTP_REFERER'] ?? '',
                'metrics' => $performance_data
            );

            // Keep only last 10 measurements
            $stored_data = array_slice($stored_data, -10);
            set_transient('redco_performance_metrics', $stored_data, DAY_IN_SECONDS);

            error_log('Redco: Performance metrics tracked: ' . json_encode($performance_data));

            wp_send_json_success(array('message' => 'Performance data tracked'));
        } else {
            wp_send_json_error(array('message' => 'Invalid performance data'));
        }
    }

    /**
     * Generate critical CSS for specific URL
     */
    private function generate_critical_css_for_url($url) {
        // This would integrate with a headless browser or CSS analysis tool
        // For now, return a basic implementation
        return $this->generate_critical_css();
    }

    /**
     * Clear critical CSS cache
     */
    public function clear_critical_cache() {
        if (is_dir($this->critical_css_dir)) {
            $files = glob($this->critical_css_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Get critical CSS size in KB
     */
    public function get_critical_css_size() {
        $critical_css = $this->get_critical_css();
        return $critical_css ? number_format(strlen($critical_css) / 1024, 1) : '0';
    }

    /**
     * Get optimization statistics
     */
    public function get_optimization_stats() {
        $stats = array(
            'critical_css_files' => 0,
            'total_cache_size' => 0,
            'last_optimization' => 0,
            'pages_optimized' => 0
        );

        if (is_dir($this->critical_css_dir)) {
            $files = glob($this->critical_css_dir . '*.css');
            $stats['critical_css_files'] = count($files);
            $stats['pages_optimized'] = count($files);

            foreach ($files as $file) {
                $stats['total_cache_size'] += filesize($file);
                $mtime = filemtime($file);
                if ($mtime > $stats['last_optimization']) {
                    $stats['last_optimization'] = $mtime;
                }
            }
        }

        return $stats;
    }

    /**
     * Get module status
     */
    public function get_module_status() {
        $enabled_features = 0;
        $total_features = 6;

        if (redco_get_module_option('critical-resource-optimizer', 'critical_css', true)) $enabled_features++;
        if (redco_get_module_option('critical-resource-optimizer', 'defer_non_critical', true)) $enabled_features++;
        if (redco_get_module_option('critical-resource-optimizer', 'optimize_js', true)) $enabled_features++;
        if (redco_get_module_option('critical-resource-optimizer', 'optimize_fonts', true)) $enabled_features++;
        if (redco_get_module_option('critical-resource-optimizer', 'resource_hints', true)) $enabled_features++;
        if (redco_get_module_option('critical-resource-optimizer', 'preconnect_google_fonts', true)) $enabled_features++;

        return array(
            'enabled_features' => $enabled_features,
            'total_features' => $total_features,
            'optimization_level' => round(($enabled_features / $total_features) * 100)
        );
    }
}

// Initialize the module only if enabled and after init hook
function redco_init_critical_resource_optimizer() {
    if (redco_is_module_enabled('critical-resource-optimizer')) {
        new Redco_Critical_Resource_Optimizer();
    }
}
add_action('init', 'redco_init_critical_resource_optimizer', 10);

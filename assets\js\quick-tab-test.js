/**
 * Quick Tab Test - Simple manual testing utility
 * 
 * This script provides quick manual testing functions that can be run
 * from the browser console to check tab loading behavior.
 */

(function($) {
    'use strict';

    // Quick test functions for manual execution
    window.RedcoQuickTest = {
        
        /**
         * Test all tabs quickly (console command)
         */
        testAllTabs: function() {
            console.log('🚀 Starting quick tab test...');
            
            const tabs = [
                'dashboard',
                'diagnostic-autofix', 
                'page-cache',
                'lazy-load',
                'css-js-minifier',
                'database-cleanup',
                'heartbeat-control',
                'wordpress-core-tweaks',
                'critical-resource-optimizer'
            ];
            
            tabs.forEach((tab, index) => {
                setTimeout(() => {
                    this.testSingleTab(tab);
                }, index * 2000); // 2 second delay between tests
            });
        },

        /**
         * Test a single tab
         */
        testSingleTab: function(tabKey) {
            console.log(`🔍 Testing tab: ${tabKey}`);
            
            const startTime = Date.now();
            const originalUrl = window.location.href;
            
            // Navigate to tab
            const tabUrl = this.buildTabUrl(tabKey);
            window.location.href = tabUrl;
            
            // Wait and check content
            setTimeout(() => {
                const loadTime = Date.now() - startTime;
                const contentCheck = this.checkTabContent(tabKey);
                
                console.log(`📊 Tab ${tabKey} results:`, {
                    loadTime: loadTime + 'ms',
                    contentLoaded: contentCheck.hasContent,
                    ajaxLoaded: contentCheck.hasAjaxContent,
                    loadingHidden: contentCheck.loadingHidden,
                    status: this.getTestStatus(loadTime, contentCheck)
                });
                
                // Return to original URL after test
                setTimeout(() => {
                    window.location.href = originalUrl;
                }, 1000);
                
            }, 3000); // Wait 3 seconds for content to load
        },

        /**
         * Check current tab content
         */
        checkTabContent: function(tabKey) {
            const result = {
                hasContent: false,
                hasAjaxContent: false,
                loadingHidden: true
            };
            
            // Check basic content
            const contentSelectors = [
                '.redco-content',
                '.redco-module-content', 
                '.redco-module-settings',
                '.module-settings-content'
            ];
            
            for (let selector of contentSelectors) {
                if ($(selector).length > 0 && $(selector).is(':visible')) {
                    result.hasContent = true;
                    break;
                }
            }
            
            // Check AJAX content for dashboard
            if (tabKey === 'dashboard') {
                const statsCards = $('.redco-stats-cards .stat-card');
                let hasRealData = false;
                
                statsCards.each(function() {
                    const statNumber = $(this).find('.stat-number, .score-number, .metric-number').text().trim();
                    if (statNumber && statNumber !== '-' && statNumber !== '...' && statNumber !== 'Loading...') {
                        hasRealData = true;
                        return false;
                    }
                });
                
                result.hasAjaxContent = hasRealData;
            } else {
                result.hasAjaxContent = true; // Non-AJAX tabs
            }
            
            // Check if loading screen is hidden
            result.loadingHidden = !$('#redco-universal-loading-overlay').is(':visible');
            
            return result;
        },

        /**
         * Get test status
         */
        getTestStatus: function(loadTime, contentCheck) {
            if (!contentCheck.hasContent) {
                return '❌ FAILED - No content';
            }
            
            if (!contentCheck.hasAjaxContent) {
                return '⚠️ WARNING - AJAX content missing';
            }
            
            if (!contentCheck.loadingHidden) {
                return '⚠️ WARNING - Loading screen still visible';
            }
            
            if (loadTime > 5000) {
                return '⚠️ WARNING - Slow loading';
            }
            
            if (loadTime > 3000) {
                return '✅ PASSED - Acceptable speed';
            }
            
            return '✅ PASSED - Good speed';
        },

        /**
         * Build tab URL
         */
        buildTabUrl: function(tabKey) {
            const baseUrl = window.location.href.split('?')[0];
            return `${baseUrl}?page=redco-optimizer&tab=${tabKey}`;
        },

        /**
         * Test current tab only
         */
        testCurrentTab: function() {
            const currentTab = this.getCurrentTab();
            console.log(`🔍 Testing current tab: ${currentTab}`);
            
            const contentCheck = this.checkTabContent(currentTab);
            const loadingTime = 'N/A (already loaded)';
            
            console.log(`📊 Current tab results:`, {
                tab: currentTab,
                loadTime: loadingTime,
                contentLoaded: contentCheck.hasContent,
                ajaxLoaded: contentCheck.hasAjaxContent,
                loadingHidden: contentCheck.loadingHidden,
                status: contentCheck.hasContent && contentCheck.hasAjaxContent && contentCheck.loadingHidden ? '✅ PASSED' : '❌ ISSUES DETECTED'
            });
            
            return contentCheck;
        },

        /**
         * Get current tab from URL
         */
        getCurrentTab: function() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('tab') || 'dashboard';
        },

        /**
         * Check loading screen status
         */
        checkLoadingScreen: function() {
            const $overlay = $('#redco-universal-loading-overlay');
            const isVisible = $overlay.is(':visible');
            const hasOverlay = $overlay.length > 0;
            
            console.log('🔍 Loading screen status:', {
                overlayExists: hasOverlay,
                isVisible: isVisible,
                status: isVisible ? '⚠️ Loading screen is visible' : '✅ Loading screen is hidden'
            });
            
            return { hasOverlay, isVisible };
        },

        /**
         * Force hide loading screen (for testing)
         */
        hideLoadingScreen: function() {
            const $overlay = $('#redco-universal-loading-overlay');
            if ($overlay.length > 0) {
                $overlay.removeClass('show');
                setTimeout(() => {
                    $overlay.remove();
                }, 500);
                console.log('✅ Loading screen manually hidden');
            } else {
                console.log('ℹ️ No loading screen found');
            }
        },

        /**
         * Check for JavaScript errors
         */
        checkForErrors: function() {
            // This would need to be enhanced to actually capture errors
            console.log('🔍 Check browser console for any JavaScript errors');
            console.log('ℹ️ Look for:');
            console.log('  - Failed AJAX requests');
            console.log('  - Missing resources (404s)');
            console.log('  - JavaScript exceptions');
            console.log('  - CSS loading issues');
        },

        /**
         * Performance check
         */
        checkPerformance: function() {
            if (window.performance && window.performance.timing) {
                const timing = window.performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                
                console.log('📊 Performance metrics:', {
                    totalLoadTime: loadTime + 'ms',
                    domReadyTime: domReady + 'ms',
                    status: loadTime < 3000 ? '✅ Good performance' : '⚠️ Slow loading'
                });
            } else {
                console.log('ℹ️ Performance timing not available');
            }
        },

        /**
         * Full diagnostic
         */
        runFullDiagnostic: function() {
            console.log('🔬 Running full diagnostic...');
            console.log('================================');
            
            this.testCurrentTab();
            this.checkLoadingScreen();
            this.checkPerformance();
            this.checkForErrors();
            
            console.log('================================');
            console.log('✅ Diagnostic complete');
        }
    };

    // Auto-initialize when document is ready
    $(document).ready(function() {
        // Only initialize on Redco Optimizer admin pages
        if (window.location.href.includes('page=redco-optimizer')) {
            console.log('🔧 Redco Quick Test utilities loaded');
            console.log('📝 Available commands:');
            console.log('  RedcoQuickTest.testCurrentTab() - Test current tab');
            console.log('  RedcoQuickTest.testAllTabs() - Test all tabs (takes time)');
            console.log('  RedcoQuickTest.checkLoadingScreen() - Check loading screen status');
            console.log('  RedcoQuickTest.hideLoadingScreen() - Force hide loading screen');
            console.log('  RedcoQuickTest.runFullDiagnostic() - Run complete diagnostic');
        }
    });

})(jQuery);

/**
 * Modern Toast Notification System for WebP Module
 * Professional, accessible, and responsive toast notifications
 */

/* Toast Container */
.redco-toast-container {
    position: fixed;
    top: 32px; /* Below WordPress admin bar */
    right: 20px;
    z-index: 999999;
    max-width: 400px;
    pointer-events: none;
}

/* Individual Toast */
.redco-toast {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 12px;
    padding: 16px 20px;
    pointer-events: auto;
    position: relative;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid #e9ecef;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    min-height: 60px;
}

/* Toast States */
.redco-toast.show {
    transform: translateX(0);
}

.redco-toast.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* Toast Types */
.redco-toast.success {
    border-left-color: #4CAF50;
}

.redco-toast.error {
    border-left-color: #f44336;
}

.redco-toast.warning {
    border-left-color: #ff9800;
}

.redco-toast.info {
    border-left-color: #2196F3;
}

/* Toast Icon */
.redco-toast-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
}

.redco-toast.success .redco-toast-icon {
    background: #4CAF50;
    color: white;
}

.redco-toast.error .redco-toast-icon {
    background: #f44336;
    color: white;
}

.redco-toast.warning .redco-toast-icon {
    background: #ff9800;
    color: white;
}

.redco-toast.info .redco-toast-icon {
    background: #2196F3;
    color: white;
}

.redco-toast-icon .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    line-height: 1;
}

/* Toast Content */
.redco-toast-content {
    flex: 1;
    min-width: 0;
}

.redco-toast-title {
    font-weight: 600;
    font-size: 14px;
    line-height: 1.4;
    margin: 0 0 4px 0;
    color: #1a1a1a;
}

.redco-toast-message {
    font-size: 13px;
    line-height: 1.4;
    color: #666;
    margin: 0;
}

/* Toast Close Button */
.redco-toast-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #999;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-toast-close:hover {
    background: #f5f5f5;
    color: #666;
}

.redco-toast-close .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Progress Bar */
.redco-toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.redco-toast-progress-bar {
    height: 100%;
    background: currentColor;
    transition: width 0.1s linear;
    opacity: 0.7;
}

.redco-toast.success .redco-toast-progress-bar {
    background: #4CAF50;
}

.redco-toast.error .redco-toast-progress-bar {
    background: #f44336;
}

.redco-toast.warning .redco-toast-progress-bar {
    background: #ff9800;
}

.redco-toast.info .redco-toast-progress-bar {
    background: #2196F3;
}

/* Action Button */
.redco-toast-action {
    margin-top: 8px;
}

.redco-toast-action button {
    background: none;
    border: 1px solid currentColor;
    border-radius: 4px;
    color: inherit;
    cursor: pointer;
    font-size: 12px;
    padding: 4px 12px;
    transition: all 0.2s ease;
}

.redco-toast.success .redco-toast-action button {
    color: #4CAF50;
}

.redco-toast.error .redco-toast-action button {
    color: #f44336;
}

.redco-toast.warning .redco-toast-action button {
    color: #ff9800;
}

.redco-toast.info .redco-toast-action button {
    color: #2196F3;
}

.redco-toast-action button:hover {
    background: currentColor;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-toast-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }
    
    .redco-toast {
        padding: 14px 16px;
        margin-bottom: 8px;
    }
    
    .redco-toast-title {
        font-size: 13px;
    }
    
    .redco-toast-message {
        font-size: 12px;
    }
}

/* Accessibility */
.redco-toast[role="alert"] {
    /* Announced by screen readers */
}

.redco-toast:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* Animation for multiple toasts */
.redco-toast:nth-child(1) { animation-delay: 0ms; }
.redco-toast:nth-child(2) { animation-delay: 100ms; }
.redco-toast:nth-child(3) { animation-delay: 200ms; }
.redco-toast:nth-child(4) { animation-delay: 300ms; }
.redco-toast:nth-child(5) { animation-delay: 400ms; }

/* Slide in animation */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.redco-toast.animate-in {
    animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .redco-toast {
        border: 2px solid;
    }
    
    .redco-toast.success {
        border-color: #4CAF50;
    }
    
    .redco-toast.error {
        border-color: #f44336;
    }
    
    .redco-toast.warning {
        border-color: #ff9800;
    }
    
    .redco-toast.info {
        border-color: #2196F3;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .redco-toast {
        transition: none;
    }
    
    .redco-toast.animate-in {
        animation: none;
        transform: translateX(0);
        opacity: 1;
    }
}

/**
 * Modern Toast Notification System for WebP Module
 * Professional, accessible, and responsive toast notifications
 */

(function($) {
    'use strict';

    // Toast notification system
    window.RedcoToast = {
        container: null,
        toasts: [],
        maxToasts: 5,
        defaultDuration: 5000,

        // Initialize the toast system
        init: function() {
            if (!this.container) {
                this.createContainer();
            }
        },

        // Create the toast container
        createContainer: function() {
            this.container = $('<div class="redco-toast-container" role="region" aria-label="Notifications"></div>');
            $('body').append(this.container);
        },

        // Show a toast notification
        show: function(options) {
            this.init();

            const defaults = {
                type: 'info', // success, error, warning, info
                title: '',
                message: '',
                duration: this.defaultDuration,
                closable: true,
                action: null, // { text: 'Action', callback: function() {} }
                persistent: false // Don't auto-dismiss
            };

            const config = $.extend({}, defaults, options);

            // Limit number of toasts
            if (this.toasts.length >= this.maxToasts) {
                this.dismiss(this.toasts[0]);
            }

            const toast = this.createToast(config);
            this.toasts.push(toast);
            this.container.append(toast.element);

            // Show the toast
            setTimeout(() => {
                toast.element.addClass('show animate-in');
            }, 10);

            // Auto-dismiss if not persistent
            if (!config.persistent && config.duration > 0) {
                this.startProgressBar(toast, config.duration);
                toast.timeout = setTimeout(() => {
                    this.dismiss(toast);
                }, config.duration);
            }

            return toast;
        },

        // Create a toast element
        createToast: function(config) {
            const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

            const iconMap = {
                success: 'yes-alt',
                error: 'dismiss',
                warning: 'warning',
                info: 'info'
            };

            const titleMap = {
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                info: 'Information'
            };

            const title = config.title || titleMap[config.type] || 'Notification';
            const icon = iconMap[config.type] || 'info';

            // Add custom className if provided
            const customClass = config.className ? ` ${config.className}` : '';

            let toastHtml = `
                <div class="redco-toast ${config.type}${customClass}" id="${toastId}" role="alert" aria-live="polite" tabindex="0">
                    <div class="redco-toast-icon">
                        <span class="dashicons dashicons-${icon}"></span>
                    </div>
                    <div class="redco-toast-content">
                        <div class="redco-toast-title">${this.escapeHtml(title)}</div>
                        ${config.message ? `<div class="redco-toast-message">${this.escapeHtml(config.message)}</div>` : ''}
                        ${config.action ? `<div class="redco-toast-action"><button type="button">${this.escapeHtml(config.action.text)}</button></div>` : ''}
                    </div>
            `;

            if (config.closable) {
                toastHtml += `
                    <button type="button" class="redco-toast-close" aria-label="Close notification">
                        <span class="dashicons dashicons-no-alt"></span>
                    </button>
                `;
            }

            if (!config.persistent && config.duration > 0) {
                toastHtml += `
                    <div class="redco-toast-progress">
                        <div class="redco-toast-progress-bar"></div>
                    </div>
                `;
            }

            toastHtml += '</div>';

            const element = $(toastHtml);

            const toast = {
                id: toastId,
                element: element,
                config: config,
                timeout: null
            };

            // Bind events
            this.bindToastEvents(toast);

            return toast;
        },

        // Bind events to toast
        bindToastEvents: function(toast) {
            const self = this;

            // Close button
            toast.element.find('.redco-toast-close').on('click', function() {
                self.dismiss(toast);
            });

            // Action button
            if (toast.config.action && toast.config.action.callback) {
                toast.element.find('.redco-toast-action button').on('click', function() {
                    toast.config.action.callback();
                    if (toast.config.action.dismissOnClick !== false) {
                        self.dismiss(toast);
                    }
                });
            }

            // Keyboard support
            toast.element.on('keydown', function(e) {
                if (e.key === 'Escape' && toast.config.closable) {
                    self.dismiss(toast);
                }
            });

            // Pause auto-dismiss on hover
            if (!toast.config.persistent) {
                toast.element.on('mouseenter', function() {
                    if (toast.timeout) {
                        clearTimeout(toast.timeout);
                        toast.timeout = null;
                    }
                    toast.element.find('.redco-toast-progress-bar').css('animation-play-state', 'paused');
                });

                toast.element.on('mouseleave', function() {
                    if (!toast.config.persistent && toast.config.duration > 0) {
                        const remaining = self.getRemainingTime(toast);
                        if (remaining > 0) {
                            toast.timeout = setTimeout(() => {
                                self.dismiss(toast);
                            }, remaining);
                            toast.element.find('.redco-toast-progress-bar').css('animation-play-state', 'running');
                        }
                    }
                });
            }
        },

        // Start progress bar animation
        startProgressBar: function(toast, duration) {
            const progressBar = toast.element.find('.redco-toast-progress-bar');
            if (progressBar.length) {
                progressBar.css({
                    'width': '100%',
                    'transition': `width ${duration}ms linear`
                });

                setTimeout(() => {
                    progressBar.css('width', '0%');
                }, 10);
            }
        },

        // Get remaining time for progress bar
        getRemainingTime: function(toast) {
            const progressBar = toast.element.find('.redco-toast-progress-bar');
            if (progressBar.length) {
                const currentWidth = parseFloat(progressBar.css('width'));
                const totalWidth = parseFloat(progressBar.parent().css('width'));
                const percentage = currentWidth / totalWidth;
                return Math.max(0, percentage * toast.config.duration);
            }
            return 0;
        },

        // Dismiss a toast
        dismiss: function(toast) {
            if (toast.timeout) {
                clearTimeout(toast.timeout);
            }

            toast.element.removeClass('show').addClass('hide');

            setTimeout(() => {
                toast.element.remove();
                this.toasts = this.toasts.filter(t => t.id !== toast.id);
            }, 300);
        },

        // Dismiss all toasts
        dismissAll: function() {
            this.toasts.forEach(toast => this.dismiss(toast));
        },

        // Utility function to escape HTML
        escapeHtml: function(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // Convenience methods
        success: function(message, options = {}) {
            return this.show($.extend({ type: 'success', message: message }, options));
        },

        error: function(message, options = {}) {
            return this.show($.extend({ type: 'error', message: message, duration: 8000 }, options));
        },

        warning: function(message, options = {}) {
            return this.show($.extend({ type: 'warning', message: message, duration: 6000 }, options));
        },

        info: function(message, options = {}) {
            return this.show($.extend({ type: 'info', message: message }, options));
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        RedcoToast.init();
    });

})(jQuery);

<?php
require_once 'wp-config.php';
require_once 'wp-load.php';

$attachment_id = 72; // One of the converted images
$metadata = wp_get_attachment_metadata($attachment_id);
echo "Attachment metadata for ID $attachment_id:\n";
print_r($metadata);

echo "\nAttachment file path:\n";
$file_path = get_attached_file($attachment_id);
echo $file_path . "\n";

echo "\nFile exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "\n";

echo "\nWebP conversion data:\n";
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
print_r($conversion_data);
